'use client'
import dynamic from 'next/dynamic'
import { Loader2 } from 'lucide-react'
import { Suspense } from 'react'
import { MainLayout } from '@/components/layout/main-layout'

// Dynamically load the client-side ProjectsPage. Disabling SSR keeps the initial payload light.
const ProjectsPage = dynamic(() => import('@/components/projects/projects-page').then(m => m.ProjectsPage), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen flex items-center justify-center">
      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
    </div>
  ),
})

export default function Home() {
  return (
    <MainLayout>
      <Suspense
        fallback={
          <div className="flex-1 flex items-center justify-center">
            <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          </div>
        }
      >
        <ProjectsPage />
      </Suspense>
    </MainLayout>
  )
}
