export interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
  metadata?: any;
}

export interface FunctionCall {
  name: string;
  arguments: Record<string, any>;
}

export interface ChatResponse {
  content: string;
  functionCalls?: FunctionCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface StreamingChatResponse {
  stream: ReadableStream<Uint8Array>;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface AIProvider {
  name: string;
  displayName: string;
  chat(
    messages: ChatMessage[],
    functions?: {
      name: string;
      description: string;
      parameters: Record<string, unknown>;
    }[]
  ): Promise<ChatResponse>;
  chatStream?(
    messages: ChatMessage[],
    functions?: {
      name: string;
      description: string;
      parameters: Record<string, unknown>;
    }[]
  ): Promise<StreamingChatResponse>;
  isConfigured(): boolean;
}

export interface ProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}
