'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Folder, Calendar, FileText, CheckCircle2, XCircle, Loader, Upload } from 'lucide-react'
import React from 'react';

interface Project {
  id: string
  name: string
  description?: string
  path: string
  language?: string
  framework?: string
  isIndexed: boolean
  indexingStatus: string
  totalFiles: number
  indexedFiles: number
  totalLines: number
  createdAt: string
  lastIndexedAt?: string
}

interface ProjectCardProps {
  project: Project
  onClick: () => void
}

export function ProjectCard({ project, onClick }: ProjectCardProps) {
  const statusMap: Record<string, { icon: React.JSX.Element; text: string; color: string }> = {
    completed: {
      icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
      text: 'Indexed',
      color: 'text-green-500',
    },
    indexing: {
      icon: <Loader className="h-4 w-4 text-blue-500 animate-spin" />,
      text: 'Indexing...',
      color: 'text-blue-500',
    },
    failed: {
      icon: <XCircle className="h-4 w-4 text-red-500" />,
      text: 'Failed',
      color: 'text-red-500',
    },
    pending: {
      icon: <Loader className="h-4 w-4 text-slate-500" />,
      text: 'Pending',
      color: 'text-slate-500',
    },
  };
  const StatusInfo = statusMap[project.indexingStatus] || statusMap.pending;

  const getProgressPercentage = () => {
    if (project.totalFiles === 0) return 0
    return Math.round((project.indexedFiles / project.totalFiles) * 100)
  }

  return (
    <Card
      className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-all duration-200 hover:shadow-md"
      onClick={onClick}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-4 flex-1 min-w-0">
            <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg text-slate-900 dark:text-slate-50 truncate mb-1">
                {project.name}
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2">
                {project.description || 'No description provided.'}
              </p>
            </div>
          </div>

          <div className={`flex items-center gap-2 ${StatusInfo.color}`}>
            {StatusInfo.icon}
            <span className="font-medium text-sm">{StatusInfo.text}</span>
          </div>
        </div>

        {/* Progress Bar (only show if indexing or has files) */}
        {(project.indexingStatus === 'indexing' || project.totalFiles > 0) && (
          <div className="mb-4">
            <div className="flex items-center justify-between text-xs text-slate-600 dark:text-slate-400 mb-2">
              <span>Indexing Progress</span>
              <span>{project.indexedFiles}/{project.totalFiles} files ({getProgressPercentage()}%)</span>
            </div>
            <Progress
              value={getProgressPercentage()}
              className="h-2"
            />
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Language and Framework Tags */}
            <div className="flex items-center gap-2">
              {project.language && (
                <Badge variant="outline" className="text-xs">
                  {project.language}
                </Badge>
              )}
              {project.framework && (
                <Badge variant="outline" className="text-xs">
                  {project.framework}
                </Badge>
              )}
            </div>

            {/* File Count */}
            <div className="flex items-center gap-1 text-sm text-slate-600 dark:text-slate-400">
              <FileText className="w-4 h-4" />
              <span>{project.totalFiles} files</span>
            </div>
          </div>

          {/* Created Date */}
          <div className="flex items-center gap-1 text-sm text-slate-500 dark:text-slate-500">
            <Calendar className="w-4 h-4" />
            <span>{new Date(project.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </Card>
  )
}
