'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { Folder, Calendar, FileText, CheckCircle2, XCircle, Loader, Upload, RefreshCw, Play, Zap } from 'lucide-react'
import React, { useState, useEffect } from 'react';

interface Project {
  id: string
  name: string
  description?: string
  path: string
  language?: string
  framework?: string
  isIndexed: boolean
  indexingStatus: string
  totalFiles: number
  indexedFiles: number
  totalLines: number
  createdAt: string
  lastIndexedAt?: string
}

interface ProjectStats {
  projectId: string
  totalFiles: number
  indexedFiles: number
  unindexedFiles: number
  totalLines: number
  totalSize: number
  needsReindexing: boolean
  indexingStatus: string
  lastIndexedAt?: string
  canStartIndexing: boolean
}

interface ProjectCardProps {
  project: Project
  onClick: () => void
  onRefresh?: () => void
}

export function ProjectCard({ project, onClick, onRefresh }: ProjectCardProps) {
  const [stats, setStats] = useState<ProjectStats | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isIndexing, setIsIndexing] = useState(false)

  const statusMap: Record<string, { icon: React.JSX.Element; text: string; color: string }> = {
    completed: {
      icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
      text: 'Indexed',
      color: 'text-green-500',
    },
    indexing: {
      icon: <Loader className="h-4 w-4 text-blue-500 animate-spin" />,
      text: 'Indexing...',
      color: 'text-blue-500',
    },
    failed: {
      icon: <XCircle className="h-4 w-4 text-red-500" />,
      text: 'Failed',
      color: 'text-red-500',
    },
    pending: {
      icon: <Loader className="h-4 w-4 text-slate-500" />,
      text: 'Pending',
      color: 'text-slate-500',
    },
  };
  const StatusInfo = statusMap[project.indexingStatus] || statusMap.pending;

  // Fetch real-time stats
  const fetchStats = async () => {
    try {
      setIsRefreshing(true)
      const response = await fetch(`/api/projects/${project.id}/stats`)
      if (response.ok) {
        const data = await response.json()
        setStats(data)
        onRefresh?.()
      }
    } catch (error) {
      console.error('Failed to fetch project stats:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  // Start indexing
  const startIndexing = async (type: 'full' | 'incremental' | 'changed') => {
    try {
      setIsIndexing(true)
      const response = await fetch(`/api/projects/${project.id}/index`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          incremental: type === 'incremental',
          changedOnly: type === 'changed'
        })
      })

      if (response.ok) {
        // Start polling for progress
        setTimeout(fetchStats, 1000)
      }
    } catch (error) {
      console.error('Failed to start indexing:', error)
    } finally {
      setIsIndexing(false)
    }
  }

  // Load stats on mount
  useEffect(() => {
    fetchStats()
  }, [project.id])

  // Use stats if available, otherwise fall back to project data
  const displayStats = stats || {
    totalFiles: project.totalFiles,
    indexedFiles: project.indexedFiles,
    unindexedFiles: project.totalFiles - project.indexedFiles,
    canStartIndexing: project.totalFiles > project.indexedFiles,
    needsReindexing: project.totalFiles > project.indexedFiles
  }

  const getProgressPercentage = () => {
    if (displayStats.totalFiles === 0) return 0
    return Math.round((displayStats.indexedFiles / displayStats.totalFiles) * 100)
  }

  return (
    <Card
      className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-all duration-200 hover:shadow-md"
      onClick={onClick}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-4 flex-1 min-w-0">
            <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg text-slate-900 dark:text-slate-50 truncate mb-1">
                {project.name}
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2">
                {project.description || 'No description provided.'}
              </p>
            </div>
          </div>

          <div className={`flex items-center gap-2 ${StatusInfo.color}`}>
            {StatusInfo.icon}
            <span className="font-medium text-sm">{StatusInfo.text}</span>
          </div>
        </div>

        {/* Progress Bar (only show if indexing or has files) */}
        {(project.indexingStatus === 'indexing' || displayStats.totalFiles > 0) && (
          <div className="mb-4">
            <div className="flex items-center justify-between text-xs text-slate-600 dark:text-slate-400 mb-2">
              <span>Indexing Progress</span>
              <span>{displayStats.indexedFiles}/{displayStats.totalFiles} files ({getProgressPercentage()}%)</span>
            </div>
            <Progress
              value={getProgressPercentage()}
              className="h-2"
            />
          </div>
        )}

        {/* Action Buttons */}
        {displayStats.canStartIndexing && (
          <div className="mb-4 flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation()
                startIndexing('changed')
              }}
              disabled={isIndexing || project.indexingStatus === 'indexing'}
              className="flex items-center gap-1"
            >
              <Zap className="w-3 h-3" />
              Index {displayStats.unindexedFiles} files
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation()
                fetchStats()
              }}
              disabled={isRefreshing}
              className="flex items-center gap-1"
            >
              <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Language and Framework Tags */}
            <div className="flex items-center gap-2">
              {project.language && (
                <Badge variant="outline" className="text-xs">
                  {project.language}
                </Badge>
              )}
              {project.framework && (
                <Badge variant="outline" className="text-xs">
                  {project.framework}
                </Badge>
              )}
            </div>

            {/* File Count */}
            <div className="flex items-center gap-1 text-sm text-slate-600 dark:text-slate-400">
              <FileText className="w-4 h-4" />
              <span>{displayStats.totalFiles} files</span>
              {displayStats.unindexedFiles > 0 && (
                <span className="text-orange-500">
                  ({displayStats.unindexedFiles} unindexed)
                </span>
              )}
            </div>
          </div>

          {/* Created Date */}
          <div className="flex items-center gap-1 text-sm text-slate-500 dark:text-slate-500">
            <Calendar className="w-4 h-4" />
            <span>{new Date(project.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </Card>
  )
}
