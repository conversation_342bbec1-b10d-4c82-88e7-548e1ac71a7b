{"name": "erzen-xen-api", "version": "1.0.1", "description": "A comprehensive global system for authentication, messaging, video, photos, and more, similar to Google services like Google Drive and Google Chat.", "author": "<PERSON><PERSON><PERSON> <[<EMAIL>]>", "private": true, "license": "AGPL-3.0", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "r": "infisical run --watch -- yarn start:dev", "start": "nest start", "start:default": "nest start --watch", "start:dev": "nest start -b swc -w --type-check", "start:debug": "nest start -b swc --debug -w", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "sentry:sourcemaps": "sentry-clip sourcemaps inject --org xenbit --project xen-auth ./dist && sentry-cli sourcemaps upload --org xenbit --project xen-auth ./dist", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@apollo/server": "^4.11.3", "@datalust/winston-seq": "^2.0.0", "@google/genai": "^0.6.1", "@google/generative-ai": "^0.24.0", "@mozilla/readability": "^0.6.0", "@nest-lab/throttler-storage-redis": "^1.0.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/apollo": "^13.0.2", "@nestjs/bullmq": "^10.2.2", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^11.0.17", "@nestjs/config": "^3.2.3", "@nestjs/core": "^11.0.8", "@nestjs/cqrs": "^10.2.7", "@nestjs/devtools-integration": "^0.2.0", "@nestjs/event-emitter": "^2.0.4", "@nestjs/graphql": "^13.0.2", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.5", "@nestjs/platform-socket.io": "^10.4.7", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.2.1", "@nestjs/websockets": "^10.4.7", "@prisma/client": "^5.22.0", "@sentry/cli": "^2.41.0", "@sentry/node": "^8.54.0", "@sentry/profiling-node": "^8.54.0", "@sentry/tracing": "^7.120.3", "@socket.io/admin-ui": "^0.5.1", "@types/passport-linkedin-oauth2": "^1.5.6", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "aws-sdk": "^2.1691.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bullmq": "^5.28.1", "cache-manager": "5.7.6", "cache-manager-redis-store": "3.0.1", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.5", "cookie-parser": "^1.4.7", "crawlee": "^3.13.0", "dotenv": "^16.4.5", "ejs": "^3.1.10", "elevenlabs": "^0.36.4", "graphql": "^16.10.0", "helmet": "^8.0.0", "ioredis": "^5.4.1", "jsdom": "^26.1.0", "nest-winston": "^1.9.7", "newrelic": "^12.11.1", "openai": "^4.80.1", "otplib": "^12.0.1", "p-timeout": "^6.1.4", "passport": "^0.7.0", "passport-discord": "^0.1.4", "passport-facebook": "^3.0.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-linkedin-oauth2": "^2.0.0", "passport-local": "^1.0.0", "playwright": "^1.50.1", "posthog-node": "^4.11.5", "qrcode": "^1.5.4", "redis": "^4.7.0", "reflect-metadata": "^0.2.0", "rss-parser": "^3.13.0", "rxjs": "^7.8.1", "socket.io-redis": "^6.1.1", "typeorm": "^0.3.22", "web-push": "^3.6.7", "winston": "^3.15.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.8", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.8", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.17.14", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.12.2", "conventional-changelog-cli": "^5.0.0", "eslint": "^9.15.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.22.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}