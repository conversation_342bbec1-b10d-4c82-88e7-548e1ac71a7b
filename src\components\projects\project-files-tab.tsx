'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  File, 
  Folder, 
  Search, 
  RefreshCw, 
  CheckCircle2, 
  XCircle, 
  Loader2,
  FileText,
  Code,
  Image,
  FileJson,
  FileCode
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProjectFile {
  id: string
  path: string
  name: string
  extension: string
  size: number
  lines: number
  isIndexed: boolean
  lastIndexedAt?: string
}

interface ProjectFilesTabProps {
  projectId: string
}

export function ProjectFilesTab({ projectId }: ProjectFilesTabProps) {
  const [files, setFiles] = useState<ProjectFile[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [isRefreshing, setIsRefreshing] = useState(false)

  useEffect(() => {
    fetchFiles()
  }, [projectId])

  const fetchFiles = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/projects/${projectId}/index`)
      if (response.ok) {
        const data = await response.json()
        setFiles(data.files || [])
      }
    } catch (error) {
      console.error('Error fetching files:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const refreshFiles = async () => {
    setIsRefreshing(true)
    await fetchFiles()
    setIsRefreshing(false)
  }

  const getFileIcon = (extension: string) => {
    const ext = extension.toLowerCase()
    if (['.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte'].includes(ext)) {
      return <Code className="w-4 h-4 text-blue-500" />
    }
    if (['.json', '.yaml', '.yml', '.xml'].includes(ext)) {
      return <FileJson className="w-4 h-4 text-green-500" />
    }
    if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
      return <Image className="w-4 h-4 text-purple-500" />
    }
    if (['.md', '.txt', '.doc', '.docx'].includes(ext)) {
      return <FileText className="w-4 h-4 text-slate-500" />
    }
    if (['.py', '.java', '.cpp', '.c', '.go', '.rs', '.php'].includes(ext)) {
      return <FileCode className="w-4 h-4 text-orange-500" />
    }
    return <File className="w-4 h-4 text-slate-400" />
  }

  const getStatusBadge = (file: ProjectFile) => {
    if (file.isIndexed) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          <CheckCircle2 className="w-3 h-3 mr-1" />
          Indexed
        </Badge>
      )
    }
    return (
      <Badge variant="secondary" className="bg-slate-100 text-slate-800 dark:bg-slate-900/20 dark:text-slate-400">
        <XCircle className="w-3 h-3 mr-1" />
        Not Indexed
      </Badge>
    )
  }

  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.path.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-slate-600 dark:text-slate-400">Loading files...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b bg-white dark:bg-slate-800">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Project Files</h2>
          <Button
            onClick={refreshFiles}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={cn("w-4 h-4 mr-2", isRefreshing && "animate-spin")} />
            Refresh
          </Button>
        </div>
        
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Files List */}
      <div className="flex-1 overflow-y-auto p-6">
        {filteredFiles.length === 0 ? (
          <div className="text-center py-16">
            <Folder className="w-16 h-16 text-slate-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
              {searchQuery ? 'No files found' : 'No files uploaded'}
            </h3>
            <p className="text-slate-500 dark:text-slate-400">
              {searchQuery 
                ? 'Try adjusting your search query'
                : 'Upload some files to get started'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {filteredFiles.map((file) => (
              <Card key={file.id} className="hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      {getFileIcon(file.extension)}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">{file.name}</p>
                        <p className="text-xs text-slate-500 truncate">{file.path}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-xs text-slate-500">
                        {formatFileSize(file.size)} • {file.lines} lines
                      </div>
                      {getStatusBadge(file)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
