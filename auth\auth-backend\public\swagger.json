{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Info"]}}, "/health": {"get": {"operationId": "AppController_getHealth", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Info"]}}, "/version": {"get": {"operationId": "AppController_getVersion", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Info"]}}, "/author": {"get": {"operationId": "AppController_getAuthor", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Info"]}}, "/system-info": {"get": {"operationId": "AppController_getSystemInfo", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Info"]}}, "/infrastructure-info": {"get": {"operationId": "AppController_getInfrastructureInfo", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Info"]}}, "/docs": {"get": {"operationId": "AppController_getScalarDocs", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Info"]}}, "/v1/auth/register": {"post": {"operationId": "AuthController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/login": {"post": {"operationId": "AuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/info": {"get": {"operationId": "AuthController_me", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/refresh": {"post": {"operationId": "AuthController_refresh", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/logout": {"post": {"operationId": "AuthController_logout", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/mfa/setup": {"post": {"operationId": "AuthController_setupMfa", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/mfa/setup/verify": {"post": {"operationId": "AuthController_setupMfaSecond", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/mfa/verify": {"post": {"operationId": "AuthController_verifyMfa", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MfaDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/mfa/disable": {"patch": {"operationId": "AuthController_disableMfa", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/reset-password": {"post": {"operationId": "AuthController_resetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/reset-password/verify/{token}": {"get": {"operationId": "AuthController_verifyResetPassword", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/change-password": {"patch": {"operationId": "AuthController_changePassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Authentication"]}}, "/v1/auth/arp-transfer": {"get": {"operationId": "AuthController_transferAuth", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Authentication"]}}, "/oauth/applications/register": {"post": {"operationId": "OAuthProviderController_registerApplication", "summary": "Register a new OAuth client application", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/oauth/applications/dev": {"get": {"operationId": "OAuthProviderController_getUserApplications", "summary": "Returns all the applications created by the user", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/oauth/applications/edit": {"put": {"operationId": "OAuthProviderController_editUserApplication", "summary": "Edit an existing OAuth client application", "parameters": [{"name": "application_id", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/oauth/authorize": {"get": {"operationId": "OAuthProviderController_authorize", "summary": "Authorize an OAuth client", "parameters": [{"name": "client_id", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "redirect_uri", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "scope", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "state", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "response_type", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/oauth/token": {"post": {"operationId": "OAuthProviderController_token", "summary": "Exchange authorization code for tokens", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/oauth/consent": {"get": {"operationId": "OAuthProviderController_getConsentScreen", "summary": "Get consent screen information", "parameters": [{"name": "client_id", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "scope", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["OAuth Provider"]}, "post": {"operationId": "OAuthProviderController_grantConsent", "summary": "Grant consent for an application", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/oauth/revoke": {"post": {"operationId": "OAuthProviderController_revokeAccess", "summary": "Revoke application access", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/oauth/applications": {"get": {"operationId": "OAuthProviderController_listApplications", "summary": "List authorized applications for user", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/oauth/applications/rotate-secret": {"post": {"operationId": "OAuthProviderController_rotateClientSecret", "summary": "Rotate client secret", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["OAuth Provider"]}}, "/v1/user/active-sessions": {"get": {"operationId": "UserController_activeSessions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["User"]}}, "/v1/user/events": {"get": {"operationId": "UserController_events", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["User"]}}, "/v1/user/change-fullName": {"patch": {"operationId": "UserController_changeFullName", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeNameDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["User"]}}, "/v1/user/change-birthdate": {"patch": {"operationId": "UserController_changeBirthDate", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeBirthdateDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["User"]}}, "/v1/user/change-profilePicture": {"patch": {"operationId": "UserController_changeProfilePicture", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePhotoDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["User"]}}, "/v1/user/revoke-token": {"patch": {"operationId": "UserController_revokeToken", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["User"]}}, "/v1/user/change-ip": {"get": {"operationId": "UserController_changeIP", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["User"]}}, "/v1/external/oauth/google": {"get": {"operationId": "ExternalOAuthController_googleAuth", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/google/redirect": {"get": {"operationId": "ExternalOAuthController_googleAuthRedirect", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/github": {"get": {"operationId": "ExternalOAuthController_githubAuth", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/github/redirect": {"get": {"operationId": "ExternalOAuthController_githubAuthRedirect", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/linkedin": {"get": {"operationId": "ExternalOAuthController_linkedinAuth", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/linkedin/redirect": {"get": {"operationId": "ExternalOAuthController_linkedinAuthRedirect", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/discord": {"get": {"operationId": "ExternalOAuthController_discordAuth", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/discord/redirect": {"get": {"operationId": "ExternalOAuthController_discordAuthRedirect", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/facebook": {"get": {"operationId": "ExternalOAuthController_facebookAuth", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/external/oauth/facebook/redirect": {"get": {"operationId": "ExternalOAuthController_facebookAuthRedirect", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["External OAuth"]}}, "/v1/collection/create": {"post": {"operationId": "CollectionController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAlbumDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Collections"]}}, "/v1/collection/update": {"put": {"operationId": "CollectionController_update", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAlbumDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Collections"]}}, "/v1/collection/delete": {"delete": {"operationId": "CollectionController_delete", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAlbumDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Collections"]}}, "/v1/collection/list": {"get": {"operationId": "CollectionController_getMy", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Collections"]}}, "/v1/photo/create": {"post": {"operationId": "PhotoController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePhotoDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Photos"]}}, "/v1/photo/create-multiple": {"post": {"operationId": "PhotoController_createMultiple", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"201": {"description": ""}}, "tags": ["Photos"]}}, "/v1/photo": {"get": {"operationId": "PhotoController_getPhotos", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Photos"]}}, "/v1/photo/update": {"put": {"operationId": "PhotoController_updatePhoto", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePhotoDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Photos"]}}, "/v1/photo/delete": {"delete": {"operationId": "PhotoController_deletePhoto", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeletePhotoDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Photos"]}}, "/v1/photo/delete-multiple": {"delete": {"operationId": "PhotoController_deleteMultiplePhotos", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": ""}}, "tags": ["Photos"]}}, "/v1/photo/{id}": {"get": {"operationId": "PhotoController_getPhotoById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "The unique identifier of the photo", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Photos"]}}, "/v1/photo/album/{albumId}/user": {"get": {"operationId": "PhotoController_getPhotosByAlbumIdAndUserId", "parameters": [{"name": "albumId", "required": true, "in": "path", "description": "The ID of the album which to get the photos", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Photos"]}}, "/v1/photo/albums/user": {"post": {"operationId": "PhotoController_getPhotosByAlbumIdsAndUserId", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMultipleAlbumPhotoDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Photos"]}}, "/storage/upload": {"post": {"operationId": "StorageController_uploadFile", "parameters": [], "responses": {"201": {"description": ""}}}}, "/v1/privacy-settings/initialize": {"post": {"operationId": "PrivacyController_initializePrivacySettings", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Privacy"]}}, "/v1/privacy-settings/list": {"get": {"operationId": "PrivacyController_getPrivacySettings", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Privacy"]}}, "/v1/privacy-settings/create": {"post": {"operationId": "PrivacyController_createPrivacySettings", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePrivacySettingsDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Privacy"]}}, "/v1/privacy-settings/update": {"put": {"operationId": "PrivacyController_updatePrivacySettings", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePrivacySettingsDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Privacy"]}}, "/v1/privacy-settings/delete": {"delete": {"operationId": "PrivacyController_deletePrivacySettings", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Privacy"]}}, "/messaging/send/{username}": {"post": {"operationId": "MessagingController_sendMessage", "parameters": [{"name": "username", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/conversations": {"get": {"operationId": "MessagingController_getConversations", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/messages/{conversationUserId}": {"get": {"operationId": "MessagingController_getMessages", "parameters": [{"name": "conversationUserId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/searchUsers": {"get": {"operationId": "MessagingController_searchUsers", "parameters": [{"name": "query", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/unreadMessages": {"get": {"operationId": "MessagingController_getUnreadMessages", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/delete/{messageId}": {"delete": {"operationId": "MessagingController_deleteMessage", "parameters": [{"name": "messageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/deleteConversation/{conversationUserId}": {"delete": {"operationId": "MessagingController_deleteConversation", "parameters": [{"name": "conversationUserId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/userInfo/{username}": {"get": {"operationId": "MessagingController_getUserInfo", "parameters": [{"name": "username", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/subscribe": {"post": {"operationId": "MessagingController_subscribe", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/unsubscribe": {"delete": {"operationId": "MessagingController_unsubscribe", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/groups": {"post": {"operationId": "MessagingController_createGroup", "summary": "Create a new group chat", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGroupDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Messaging"]}, "get": {"operationId": "MessagingController_getUserGroups", "summary": "Get all groups for the current user", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/groups/{groupId}": {"get": {"operationId": "MessagingController_getGroupDetails", "summary": "Get details for a specific group", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/groups/{groupId}/members": {"post": {"operationId": "MessagingController_addGroupMembers", "summary": "Add members to a group", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddGroupMemberDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Messaging"]}, "delete": {"operationId": "MessagingController_removeGroupMembers", "summary": "Remove members from a group", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddGroupMemberDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/groups/{groupId}/messages": {"post": {"operationId": "MessagingController_sendGroupMessage", "summary": "Send a message to a group", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupMessageDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Messaging"]}, "get": {"operationId": "MessagingController_getGroupMessages", "summary": "Get messages for a group", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/calls": {"post": {"operationId": "MessagingController_initiateCall", "summary": "Initiate a new call", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InitiateCallDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/calls/{callId}/join": {"post": {"operationId": "MessagingController_joinCall", "summary": "Join a call", "parameters": [{"name": "callId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/calls/{callId}/leave": {"post": {"operationId": "MessagingController_leaveCall", "summary": "Leave a call", "parameters": [{"name": "callId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Messaging"]}}, "/messaging/calls/{callId}/participants": {"get": {"operationId": "MessagingController_getCallParticipants", "summary": "Get active participants in a call", "parameters": [{"name": "callId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Messaging"]}}, "/intelligence/dev/instructions": {"get": {"operationId": "IntelligenceController_listInstructions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/instruction/{id}": {"delete": {"operationId": "IntelligenceController_deleteInstruction", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/instructions/user": {"get": {"operationId": "IntelligenceController_listUserInstructions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/applications": {"post": {"operationId": "IntelligenceController_createApplication", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateApplicationDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}, "get": {"operationId": "IntelligenceController_listApplications", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/application/{id}": {"get": {"operationId": "IntelligenceController_getApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "put": {"operationId": "IntelligenceController_updateApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "delete": {"operationId": "IntelligenceController_deleteApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/applications/billing": {"get": {"operationId": "IntelligenceController_getBillingInfo", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/intelligence/models": {"get": {"operationId": "IntelligenceController_listModels", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "post": {"operationId": "IntelligenceController_createModel", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/intelligence/models/{id}": {"get": {"operationId": "IntelligenceController_getModel", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "put": {"operationId": "IntelligenceController_updateModel", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "delete": {"operationId": "IntelligenceController_deleteModel", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/intelligence/models/bulk-add": {"post": {"operationId": "IntelligenceController_bulkAddModels", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/instruction": {"post": {"operationId": "IntelligenceController_createInstruction", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInstructionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/instruction/process": {"post": {"operationId": "IntelligenceController_processPrompt", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessUserInstructionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/dev/instruction/process/beta": {"post": {"operationId": "IntelligenceController_processPromptBeta", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessBetaUserInstructionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/plain": {"post": {"operationId": "IntelligenceController_chat<PERSON>lain", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/plain/stream": {"post": {"operationId": "IntelligenceController_chatStreamPlain", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat": {"post": {"operationId": "IntelligenceController_chat", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/stream": {"post": {"operationId": "IntelligenceController_chatStream", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/reasoning": {"post": {"operationId": "IntelligenceController_reasoning", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/reasoning/draft": {"post": {"operationId": "IntelligenceController_reasoningDraft", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/reasoning/stream": {"get": {"operationId": "IntelligenceController_streamReasoning", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/threads": {"get": {"operationId": "IntelligenceController_listChatThreads", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/thread/{id}": {"get": {"operationId": "IntelligenceController_listChatThreadMessages", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "delete": {"operationId": "IntelligenceController_deleteChatThread", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/thread/{id}/duplicate": {"post": {"operationId": "IntelligenceController_duplicateChatThread", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/thread/{id}/rename": {"put": {"operationId": "IntelligenceController_renameChatThread", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenameChatThreadDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/thread/{threadId}/message/{messageId}": {"put": {"operationId": "IntelligenceController_editThreadMessage", "parameters": [{"name": "threadId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "messageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/thread/{threadId}/branch/{messageId}": {"post": {"operationId": "IntelligenceController_branchThreadFromMessage", "parameters": [{"name": "threadId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "messageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/thread/{threadId}/retry/{messageId}": {"delete": {"operationId": "IntelligenceController_retryFromMessage", "parameters": [{"name": "threadId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "messageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/memory": {"get": {"operationId": "IntelligenceController_listUserMemory", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "delete": {"operationId": "IntelligenceController_deleteUserMemory", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/instruction": {"post": {"operationId": "IntelligenceController_createUserInstruction", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserInstructionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}, "get": {"operationId": "IntelligenceController_getUserInstructions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/instruction/{id}": {"put": {"operationId": "IntelligenceController_updateUserInstruction", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserInstructionDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "delete": {"operationId": "IntelligenceController_deleteUserInstruction", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/threads/sync": {"get": {"operationId": "IntelligenceController_syncChatThreads", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/thread/{id}/export": {"get": {"operationId": "IntelligenceController_exportChatThread", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/chat/threads/export": {"get": {"operationId": "IntelligenceController_exportAllChatThreads", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects": {"post": {"operationId": "IntelligenceController_createProject", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProjectDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}, "get": {"operationId": "IntelligenceController_listProjects", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{id}": {"get": {"operationId": "IntelligenceController_getProject", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "put": {"operationId": "IntelligenceController_updateProject", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProjectDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "delete": {"operationId": "IntelligenceController_deleteProject", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{id}/threads": {"post": {"operationId": "IntelligenceController_createProjectThread", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}, "get": {"operationId": "IntelligenceController_listProjectThreads", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{id}/files": {"post": {"operationId": "IntelligenceController_createProjectFile", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProjectFileDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}, "get": {"operationId": "IntelligenceController_listProjectFiles", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{id}/files/initialize": {"post": {"operationId": "IntelligenceController_initializeProjectFiles", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InitializeProjectFilesDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{projectId}/files/{fileId}": {"get": {"operationId": "IntelligenceController_getProjectFile", "parameters": [{"name": "projectId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "fileId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}, "put": {"operationId": "IntelligenceController_updateProjectFile", "parameters": [{"name": "projectId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "fileId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProjectFileDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{projectId}/files/{fileId}/versions": {"get": {"operationId": "IntelligenceController_getProjectFileVersions", "parameters": [{"name": "projectId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "fileId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{projectId}/files/{fileId}/revert": {"post": {"operationId": "IntelligenceController_revertProjectFile", "parameters": [{"name": "projectId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "fileId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevertFileVersionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{id}/collaborators": {"post": {"operationId": "IntelligenceController_addProjectCollaborator", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddProjectCollaboratorDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}, "get": {"operationId": "IntelligenceController_listProjectCollaborators", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/{projectId}/collaborators/{userId}": {"delete": {"operationId": "IntelligenceController_removeProjectCollaborator", "parameters": [{"name": "projectId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/projects/process-agent": {"post": {"operationId": "IntelligenceController_processAdvancedInstruction", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DevInstructionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/search/results": {"get": {"operationId": "IntelligenceController_getWebSearchResults", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/intelligence/search/source/{id}": {"get": {"operationId": "IntelligenceController_getWebSearchSource", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Intelligence"]}}, "/tts/convert": {"get": {"operationId": "TextToSpeechController_convert", "parameters": [{"name": "text", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/browser/fetch": {"get": {"operationId": "BrowserController_fetchUrl", "parameters": [{"name": "url", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/browser/search": {"get": {"operationId": "BrowserController_searchWeb", "parameters": [{"name": "query", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/browser/raw-search": {"get": {"operationId": "BrowserController_rawSearch", "parameters": [{"name": "query", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/browser/raw-fetch": {"get": {"operationId": "BrowserController_rawFetch", "parameters": [{"name": "url", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/browser/ai-search": {"get": {"operationId": "BrowserController_aiSearch", "parameters": [{"name": "query", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/browser/ai-search/stream": {"get": {"operationId": "BrowserController_aiSearchStream", "parameters": [{"name": "query", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/news/sources": {"post": {"operationId": "NewsController_createSource", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSourceDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["News"]}}, "/news/sources/{id}": {"put": {"operationId": "NewsController_updateSource", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSourceDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["News"]}, "delete": {"operationId": "NewsController_deleteSource", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["News"]}}, "/news/get-articles": {"get": {"operationId": "NewsController_getArticles", "summary": "Get paginated articles", "parameters": [{"name": "country", "required": false, "in": "query", "schema": {"example": "us", "type": "string"}}, {"name": "language", "required": false, "in": "query", "schema": {"example": "en", "type": "string"}}, {"name": "category", "required": false, "in": "query", "schema": {"example": "technology", "type": "string"}}, {"name": "sourceId", "required": false, "in": "query", "schema": {"example": 1, "type": "number"}}, {"name": "page", "required": false, "in": "query", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "schema": {"minimum": 1, "example": 10, "type": "number"}}], "responses": {"200": {"description": "List of articles."}}, "tags": ["News"]}}, "/news/get-article/{id}": {"get": {"operationId": "NewsController_getArticle", "summary": "Get article by id", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Article."}}, "tags": ["News"]}}, "/news/get-sources": {"get": {"operationId": "NewsController_getSources", "summary": "Get all sources", "parameters": [], "responses": {"200": {"description": "List of sources."}}, "tags": ["News"]}}, "/news/search": {"get": {"operationId": "NewsController_searchArticles", "summary": "Search articles by query and date range", "parameters": [{"name": "query", "required": false, "in": "query", "schema": {"example": "breaking news", "type": "string"}}, {"name": "fromDate", "required": false, "in": "query", "schema": {"example": "2023-01-01", "type": "string"}}, {"name": "toDate", "required": false, "in": "query", "schema": {"example": "2023-12-31", "type": "string"}}], "responses": {"200": {"description": "Search results."}}, "tags": ["News"]}}, "/news/filter": {"get": {"operationId": "NewsController_filterArticles", "summary": "Filter articles by categories and authors", "parameters": [{"name": "categories", "required": false, "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "authors", "required": false, "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Filtered articles."}}, "tags": ["News"]}}, "/news/sort": {"get": {"operationId": "NewsController_sortArticles", "summary": "Sort articles by published date", "parameters": [{"name": "sortOrder", "required": false, "in": "query", "description": "Sort by published date", "schema": {"example": "asc", "type": "string"}}], "responses": {"200": {"description": "Sorted articles."}}, "tags": ["News"]}}, "/news/advanced-search": {"get": {"operationId": "NewsController_advancedSearch", "summary": "Advanced search with multiple filters", "parameters": [{"name": "country", "required": false, "in": "query", "schema": {"example": "us", "type": "string"}}, {"name": "language", "required": false, "in": "query", "schema": {"example": "en", "type": "string"}}, {"name": "category", "required": false, "in": "query", "schema": {"example": "technology", "type": "string"}}, {"name": "sourceId", "required": false, "in": "query", "schema": {"example": 1, "type": "number"}}, {"name": "page", "required": false, "in": "query", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "schema": {"minimum": 1, "example": 10, "type": "number"}}, {"name": "query", "required": false, "in": "query", "schema": {"example": "breaking news", "type": "string"}}, {"name": "fromDate", "required": false, "in": "query", "schema": {"example": "2023-01-01", "type": "string"}}, {"name": "toDate", "required": false, "in": "query", "schema": {"example": "2023-12-31", "type": "string"}}, {"name": "categories", "required": false, "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "authors", "required": false, "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort by published date", "schema": {"example": "asc", "type": "string"}}], "responses": {"200": {"description": "Advanced search results."}}, "tags": ["News"]}}, "/command-control/execute": {"post": {"operationId": "CommandControlController_executeCommand", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteCommandDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Command Control"]}}, "/command-control/nodes": {"post": {"operationId": "CommandControlController_getNodes", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Command Control"]}}, "/agents": {"post": {"operationId": "AgentController_createAgent", "summary": "Create a new agent", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgentDto"}}}}, "responses": {"201": {"description": "The agent has been created"}}, "tags": ["Agents"]}, "get": {"operationId": "AgentController_getAgents", "summary": "Get all agents for the current user", "parameters": [], "responses": {"200": {"description": "List of agents"}}, "tags": ["Agents"]}}, "/agents/{id}": {"get": {"operationId": "AgentController_getAgent", "summary": "Get a specific agent by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Agent found"}, "404": {"description": "Agent not found"}}, "tags": ["Agents"]}, "put": {"operationId": "AgentController_updateAgent", "summary": "Update an existing agent", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAgentDto"}}}}, "responses": {"200": {"description": "Agent updated"}, "404": {"description": "Agent not found"}}, "tags": ["Agents"]}, "delete": {"operationId": "AgentController_deleteAgent", "summary": "Delete an agent", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Agent deleted"}, "404": {"description": "Agent not found"}}, "tags": ["Agents"]}}, "/agents/{agentId}/steps": {"post": {"operationId": "AgentController_addStep", "summary": "Add a step to an agent", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgentStepDto"}}}}, "responses": {"201": {"description": "Step added"}}, "tags": ["Agents"]}}, "/agents/{agentId}/steps/{stepId}": {"put": {"operationId": "AgentController_updateStep", "summary": "Update a step", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "stepId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAgentStepDto"}}}}, "responses": {"200": {"description": "Step updated"}}, "tags": ["Agents"]}, "delete": {"operationId": "AgentController_deleteStep", "summary": "Delete a step", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "stepId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Step deleted"}}, "tags": ["Agents"]}}, "/agents/{agentId}/credentials": {"post": {"operationId": "AgentController_addCredential", "summary": "Add a credential to an agent", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgentCredentialDto"}}}}, "responses": {"201": {"description": "Credential added"}}, "tags": ["Agents"]}}, "/agents/{agentId}/credentials/{credentialId}": {"put": {"operationId": "AgentController_updateCredential", "summary": "Update a credential", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "credentialId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAgentCredentialDto"}}}}, "responses": {"200": {"description": "Credential updated"}}, "tags": ["Agents"]}, "delete": {"operationId": "AgentController_deleteCredential", "summary": "Delete a credential", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "credentialId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Credential deleted"}}, "tags": ["Agents"]}}, "/agents/{agentId}/variables": {"post": {"operationId": "AgentController_addVariable", "summary": "Add a variable to an agent", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgentVariableDto"}}}}, "responses": {"201": {"description": "Variable added"}}, "tags": ["Agents"]}}, "/agents/{agentId}/variables/{variableId}": {"put": {"operationId": "AgentController_updateVariable", "summary": "Update a variable", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "variableId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAgentVariableDto"}}}}, "responses": {"200": {"description": "Variable updated"}}, "tags": ["Agents"]}, "delete": {"operationId": "AgentController_deleteVariable", "summary": "Delete a variable", "parameters": [{"name": "agentId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "variableId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Variable deleted"}}, "tags": ["Agents"]}}, "/agents/{id}/execute": {"post": {"operationId": "AgentController_executeAgent", "summary": "Execute an agent", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteAgentDto"}}}}, "responses": {"200": {"description": "Agent executed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentExecutionResponseDto"}}}}}, "tags": ["Agents"]}}, "/agents/{id}/executions": {"get": {"operationId": "AgentController_getExecutions", "summary": "Get execution history for an agent", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "List of executions"}}, "tags": ["Agents"]}}, "/agents/{id}/executions/{executionId}": {"get": {"operationId": "AgentController_getExecution", "summary": "Get a specific execution", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "executionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Execution found"}}, "tags": ["Agents"]}}, "/api/agents/execute": {"post": {"operationId": "ApiAgentController_executeAgent", "summary": "Execute an agent using an API key", "parameters": [{"name": "api_key", "required": true, "in": "query", "description": "API Key for authentication", "schema": {"type": "string"}}, {"name": "agent_id", "required": true, "in": "query", "description": "ID of the agent to execute", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteAgentDto"}}}}, "responses": {"200": {"description": "Agent executed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentExecutionResponseDto"}}}}}, "tags": ["Agent API"]}}, "/usage": {"get": {"operationId": "UsageController_getUserUsage", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Usage"]}}, "/usage/admin/add/{userId}": {"post": {"operationId": "UsageController_addUserRequests", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Usage"]}}, "/usage/admin/remove/{userId}": {"post": {"operationId": "UsageController_removeUserRequests", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Usage"]}}, "/usage/admin/upgrade/{userId}": {"post": {"operationId": "UsageController_upgradeUser", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["Usage"]}}, "/usage/admin/user/{userId}": {"get": {"operationId": "UsageController_getSpecificUserUsage", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Usage"]}}}, "info": {"title": "XENAuth API", "description": "The official XENAuth API documentation", "version": "1.0", "contact": {"name": "<PERSON><PERSON><PERSON>", "url": "https://erzen.tk", "email": "<EMAIL>"}, "termsOfService": "https://erzen.tk/terms", "license": {"name": "AGPL-3.0", "url": "https://www.gnu.org/licenses/agpl-3.0.en.html"}}, "tags": [], "servers": [{"url": "https://localhost:3001", "description": "LOCAL server"}, {"url": "https://api.erzen.tk", "description": "DEV server"}, {"url": "https://apis.erzen.tk", "description": "PRODUCTION server"}], "components": {"securitySchemes": {"access-token": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"RegisterDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address"}, "password": {"type": "string", "description": "User password"}, "name": {"type": "string", "description": "User full name"}, "username": {"type": "string", "description": "User username"}, "birthdate": {"format": "date-time", "type": "string", "description": "User birthdate"}, "language": {"type": "string", "description": "User preferred language"}, "timezone": {"type": "string", "description": "User timezone"}}, "required": ["email", "password", "name", "username", "birthdate", "language", "timezone"]}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address"}, "password": {"type": "string", "description": "User password"}}, "required": ["email", "password"]}, "MfaDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address"}, "password": {"type": "string", "description": "User password"}, "code": {"type": "string", "description": "MFA code"}}, "required": ["email", "password", "code"]}, "ForgotPasswordDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address"}}, "required": ["email"]}, "ChangePasswordDto": {"type": "object", "properties": {"oldPassword": {"type": "string"}, "newPassword": {"type": "string"}}, "required": ["oldPassword", "newPassword"]}, "ChangeNameDto": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"]}, "ChangeBirthdateDto": {"type": "object", "properties": {"birthdate": {"type": "string", "description": "User new birthdate"}}, "required": ["birthdate"]}, "ChangePhotoDto": {"type": "object", "properties": {"photo": {"type": "string", "format": "binary", "description": "User new photo"}}, "required": ["photo"]}, "CreateAlbumDto": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the album"}}, "required": ["title"]}, "UpdateAlbumDto": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the album"}, "id": {"type": "string", "description": "The ID of the album"}}, "required": ["title", "id"]}, "DeleteAlbumDto": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the album to delete"}}, "required": ["id"]}, "CreatePhotoDto": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL of the photo", "example": "http://example.com/photo.jpg"}, "caption": {"type": "string", "description": "The caption for the photo", "example": "A beautiful sunset"}, "albumIds": {"description": "The IDs of the albums this photo belongs to", "example": [1, 2, 3], "type": "array", "items": {"type": "string"}}}, "required": ["url"]}, "UpdatePhotoDto": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier of the photo"}, "url": {"type": "string", "description": "The URL of the photo"}, "caption": {"type": "string", "description": "The caption for the photo"}}, "required": ["id", "url"]}, "DeletePhotoDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the photo to be deleted"}}, "required": ["id"]}, "GetMultipleAlbumPhotoDto": {"type": "object", "properties": {"albumIds": {"description": "Array of album IDs to get the photos from", "type": "array", "items": {"type": "string"}}}, "required": ["albumIds"]}, "UpdatePrivacySettingsDto": {"type": "object", "properties": {}}, "MessageDto": {"type": "object", "properties": {}}, "CreateGroupDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the group", "example": "Project Team"}, "description": {"type": "string", "description": "The description of the group", "example": "Group for project discussions"}, "members": {"description": "Array of user IDs to add to the group", "example": ["user-id-1", "user-id-2"], "type": "array", "items": {"type": "string"}}}, "required": ["name", "members"]}, "AddGroupMemberDto": {"type": "object", "properties": {"userIds": {"description": "Array of user IDs to add to the group", "example": ["user-id-1", "user-id-2"], "type": "array", "items": {"type": "string"}}}, "required": ["userIds"]}, "GroupMessageDto": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the message", "example": "Hello everyone!"}}, "required": ["content"]}, "InitiateCallDto": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of call (voice or video)", "enum": ["voice", "video"], "example": "video"}, "participants": {"description": "Array of user IDs to invite to the call", "example": ["user-id-1", "user-id-2"], "type": "array", "items": {"type": "string"}}}, "required": ["type", "participants"]}, "CreateApplicationDto": {"type": "object", "properties": {}}, "CreateInstructionDto": {"type": "object", "properties": {}}, "ProcessUserInstructionDto": {"type": "object", "properties": {}}, "ProcessBetaUserInstructionDto": {"type": "object", "properties": {}}, "CreateChatDto": {"type": "object", "properties": {}}, "RenameChatThreadDto": {"type": "object", "properties": {}}, "CreateUserInstructionDto": {"type": "object", "properties": {}}, "UpdateUserInstructionDto": {"type": "object", "properties": {}}, "CreateProjectDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the project"}, "description": {"type": "string", "description": "Description of the project"}}, "required": ["name"]}, "UpdateProjectDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the project"}, "description": {"type": "string", "description": "Description of the project"}}}, "CreateProjectFileDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the file"}, "path": {"type": "string", "description": "Path of the file"}, "content": {"type": "string", "description": "Content of the file"}, "commitMsg": {"type": "string", "description": "Optional commit message"}}, "required": ["name", "path", "content"]}, "InitializeProjectFilesDto": {"type": "object", "properties": {"files": {"description": "List of files to initialize", "type": "array", "items": {"type": "string"}}}, "required": ["files"]}, "UpdateProjectFileDto": {"type": "object", "properties": {"content": {"type": "string", "description": "Content of the file"}, "commitMsg": {"type": "string", "description": "Optional commit message"}}, "required": ["content"]}, "RevertFileVersionDto": {"type": "object", "properties": {"version": {"type": "number", "description": "Version number to revert to"}, "commitMsg": {"type": "string", "description": "Optional commit message for the revert"}}, "required": ["version"]}, "AddProjectCollaboratorDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "User ID to add as collaborator"}, "role": {"type": "string", "description": "Role of the collaborator (e.g., \"editor\", \"viewer\")"}}, "required": ["userId", "role"]}, "DevInstructionDto": {"type": "object", "properties": {"instruction": {"type": "string", "description": "Instruction for the AI agent"}, "projectId": {"type": "string", "description": "Project ID to apply the instruction to"}, "threadId": {"type": "string", "description": "Thread ID for the conversation context"}}, "required": ["instruction", "projectId"]}, "CreateSourceDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the source"}, "url": {"type": "string", "description": "URL of the source"}, "country": {"type": "string", "description": "Country of the source"}, "language": {"type": "string", "description": "Language of the source"}, "category": {"type": "string", "description": "Category of the source"}}, "required": ["name", "url", "country", "language", "category"]}, "UpdateSourceDto": {"type": "object", "properties": {}}, "ExecuteCommandDto": {"type": "object", "properties": {}}, "CreateAgentDto": {"type": "object", "properties": {}}, "UpdateAgentDto": {"type": "object", "properties": {}}, "CreateAgentStepDto": {"type": "object", "properties": {}}, "UpdateAgentStepDto": {"type": "object", "properties": {}}, "CreateAgentCredentialDto": {"type": "object", "properties": {}}, "UpdateAgentCredentialDto": {"type": "object", "properties": {}}, "CreateAgentVariableDto": {"type": "object", "properties": {}}, "UpdateAgentVariableDto": {"type": "object", "properties": {}}, "ExecuteAgentDto": {"type": "object", "properties": {}}, "AgentExecutionResponseDto": {"type": "object", "properties": {}}}}, "security": [{"access-token": []}]}