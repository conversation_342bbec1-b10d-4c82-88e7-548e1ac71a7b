import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from "./types";

export class GoogleProvider implements AIProvider {
  name = "google";
  displayName = "Google AI";

  private config: ProviderConfig;

  constructor(config: ProviderConfig) {
    this.config = {
      model: "gemini-pro",
      maxTokens: 4000,
      temperature: 0.7,
      baseUrl: "https://generativelanguage.googleapis.com/v1beta",
      ...config,
    };
  }

  async chat(
    messages: ChatMessage[],
    functions?: {
      name: string;
      description: string;
      parameters: Record<string, unknown>;
    }[]
  ): Promise<ChatResponse> {
    if (!this.isConfigured()) {
      throw new Error("Google AI API key not configured");
    }

    // Convert messages to Google's format
    const contents = messages.map((msg) => ({
      role: msg.role === "assistant" ? "model" : "user",
      parts: [{ text: msg.content }],
    }));

    const body: Record<string, unknown> = {
      contents,
      generationConfig: {
        temperature: this.config.temperature,
        maxOutputTokens: this.config.maxTokens,
      },
    };

    if (functions && functions.length > 0) {
      // Gemini supports tool calling via the "tools" field
      body.tools = functions.map((fn) => ({
        type: "FUNCTION",
        functionDeclarations: [
          {
            name: fn.name,
            description: fn.description,
            parameters: fn.parameters,
          },
        ],
      }));
      // Let the model decide when to call
      body.toolConfig = { functionCallingConfig: { mode: "AUTO" } };
    }

    const response = await fetch(
      `${this.config.baseUrl}/models/${this.config.model}:generateContent?key=${this.config.apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      }
    );

    if (!response.ok) {
      throw new Error(`Google AI API error: ${response.statusText}`);
    }

    const data = await response.json();
    const candidate = data.candidates?.[0];

    if (!candidate) {
      throw new Error("No response from Google AI");
    }

    // Build plain text content from text parts (may be empty if function call only)
    let textContent = "";
    if (candidate.content?.parts) {
      textContent = (candidate.content.parts as unknown[])
        .filter((p) => (p as { text?: string }).text)
        .map((p) => (p as { text: string }).text)
        .join("\n");
    }

    // Check for function (tool) calls in parts
    let functionCalls;
    if (candidate.content?.parts) {
      for (const part of candidate.content.parts) {
        if (part.functionCall) {
          const call = part.functionCall;
          functionCalls = [
            {
              name: call.name,
              arguments: call.args || call.arguments || {},
            },
          ];
          break;
        }
      }
    }

    return {
      content: textContent,
      functionCalls,
      usage: data.usageMetadata
        ? {
            promptTokens: data.usageMetadata.promptTokenCount || 0,
            completionTokens: data.usageMetadata.candidatesTokenCount || 0,
            totalTokens: data.usageMetadata.totalTokenCount || 0,
          }
        : undefined,
    };
  }

  isConfigured(): boolean {
    return !!this.config.apiKey;
  }
}
