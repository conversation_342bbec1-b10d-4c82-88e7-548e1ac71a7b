import { Sidebar } from '@/components/layout/sidebar'
import { Header } from '@/components/navigation/header'
import { SidebarProvider } from '@/components/providers/sidebar-provider'

export function MainLayout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen">
        <Sidebar />
        <div className="flex flex-col flex-1">
          <Header />
          <main className="flex-1 p-6 bg-slate-100/50 dark:bg-slate-900">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  )
} 