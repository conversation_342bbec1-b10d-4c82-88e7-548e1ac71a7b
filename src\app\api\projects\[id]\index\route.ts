import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import {
  indexProject,
  indexChangedFiles,
  getProjectStats,
} from "@/lib/indexing";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const project = await db.project.findUnique({
      where: { id },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Check if already indexing
    if (project.indexingStatus === "indexing") {
      return NextResponse.json(
        { error: "Project is already being indexed" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json().catch(() => ({}));
    const incremental = body.incremental === true;
    const changedOnly = body.changedOnly === true;

    // Get current stats to determine what needs indexing
    const stats = await getProjectStats(id);

    let indexingPromise;
    let message;

    if (changedOnly) {
      // Only index files that have changed or are new
      indexingPromise = indexChangedFiles(id);
      message = `Indexing ${stats.unindexedFiles} changed/new files`;
    } else if (incremental) {
      // Incremental indexing (unindexed files only)
      indexingPromise = indexProject(id, undefined, true);
      message = `Incremental indexing started (${stats.unindexedFiles} files)`;
    } else {
      // Full reindexing
      indexingPromise = indexProject(id, undefined, false);
      message = `Full indexing started (${stats.totalFiles} files)`;
    }

    // Start indexing in background
    indexingPromise.catch((error) => {
      console.error("Background indexing failed:", error);
    });

    return NextResponse.json({
      message,
      status: "indexing",
      incremental,
      changedOnly,
      stats: {
        totalFiles: stats.totalFiles,
        unindexedFiles: stats.unindexedFiles,
        needsReindexing: stats.needsReindexing,
      },
    });
  } catch (error) {
    console.error("Error starting indexing:", error);
    return NextResponse.json(
      { error: "Failed to start indexing" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const project = await db.project.findUnique({
      where: { id },
      include: {
        files: {
          select: {
            id: true,
            path: true,
            name: true,
            extension: true,
            size: true,
            lines: true,
            isIndexed: true,
            lastIndexedAt: true,
          },
          orderBy: {
            path: "asc",
          },
        },
      },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    return NextResponse.json({
      project: {
        id: project.id,
        name: project.name,
        indexingStatus: project.indexingStatus,
        totalFiles: project.totalFiles,
        indexedFiles: project.indexedFiles,
        totalLines: project.totalLines,
        lastIndexedAt: project.lastIndexedAt,
      },
      files: project.files,
    });
  } catch (error) {
    console.error("Error fetching indexing status:", error);
    return NextResponse.json(
      { error: "Failed to fetch indexing status" },
      { status: 500 }
    );
  }
}
