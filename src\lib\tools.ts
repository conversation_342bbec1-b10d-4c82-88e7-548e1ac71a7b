export interface ToolCall {
  name: string;
  args: Record<string, unknown>;
}

export async function executeTool(call: ToolCall): Promise<string> {
  switch (call.name) {
    case "grep_search":
      return grepSearch(call.args);
    case "codebase_search":
      return codebaseSearch(call.args);
    case "list_dir":
      return listDir(call.args);
    case "read_file":
      return readFileSnippet(call.args);
    default:
      return `Error: Tool '${call.name}' is not implemented.`;
  }
}

import { execSync } from "child_process";
import { searchCode } from "./vector";
import { db } from "@/lib/db";

async function grepSearch(args: Record<string, unknown>): Promise<string> {
  const query = String(args.query || "");
  if (!query) return "Error: query parameter required.";

  const projectId = String(args._projectId || "");
  const caseSensitive = Boolean(args.case_sensitive);

  // If we have a project ID, search inside indexed files/chunks in the DB
  if (projectId) {
    try {
      const regex = new RegExp(query, caseSensitive ? "" : "i");
      const results: string[] = [];

      // Search small stored file contents first
      const files = await db.projectFile.findMany({
        where: {
          projectId,
          content: {
            not: null,
          },
        },
        select: {
          path: true,
          content: true,
        },
        take: 2000, // reasonable cap
      });

      for (const f of files) {
        if (!f.content) continue;
        const lines = f.content.split("\n");
        lines.forEach((line, idx) => {
          if (regex.test(line)) {
            results.push(`${f.path}:${idx + 1}: ${line.trim()}`);
          }
        });
        if (results.length >= 100) break;
      }

      // If not enough matches, search chunks of larger files
      if (results.length < 100) {
        const chunks = await db.fileChunk.findMany({
          where: {
            file: { projectId },
          },
          include: {
            file: { select: { path: true } },
          },
          take: 5000,
        });
        for (const c of chunks) {
          if (regex.test(c.content)) {
            results.push(
              `${c.file.path}:${c.startLine}: ${c.content
                .split("\n")[0]
                .trim()} ...`
            );
            if (results.length >= 100) break;
          }
        }
      }

      return results.length > 0 ? results.join("\n") : "No matches found.";
    } catch (e: unknown) {
      return `Search error: ${e instanceof Error ? e.message : String(e)}`;
    }
  }

  // Fallback: run ripgrep on filesystem (mainly for dev env)
  const include = args.include_pattern ? String(args.include_pattern) : ".";
  try {
    const cmd = `rg --line-number --with-filename --no-heading --color never "${query.replace(
      /"/g,
      '\\"'
    )}" ${include}`;
    const output = execSync(cmd, {
      encoding: "utf8",
      maxBuffer: 2 * 1024 * 1024,
    });
    return output || "No matches found.";
  } catch (err: unknown) {
    return err && typeof err === "object" && "stdout" in err
      ? String(err.stdout)
      : "No matches found.";
  }
}

async function codebaseSearch(args: Record<string, unknown>): Promise<string> {
  const query = String(args.query || "");
  if (!query) return "Error: query parameter required.";
  const projectId = "0";
  const result = await searchCode(query, projectId, 20);
  if (!result.success) return "Search failed.";

  return result.results
    .map((r) => `File: ${r.metadata.filePath}\n${r.content}`)
    .join("\n\n");
}

async function listDir(args: Record<string, unknown>): Promise<string> {
  const projectId = String(args._projectId || "");
  if (!projectId) return "Error: project id missing";
  const dirPath = (args.relative_workspace_path ||
    args.path ||
    args.dir ||
    "") as string;
  try {
    const files = await db.projectFile.findMany({
      where: {
        projectId,
        path: {
          startsWith: dirPath,
        },
      },
      select: {
        path: true,
        name: true,
      },
      take: 1000,
    });
    if (files.length === 0) return "Directory is empty or not found.";

    const listing = new Map<string, "file" | "dir">();
    const prefix = dirPath ? dirPath.replace(/\\/g, "/") + "/" : "";
    for (const f of files) {
      const remainder = f.path.slice(prefix.length);
      const [first] = remainder.split("/");
      if (!first) continue;
      if (!listing.has(first)) {
        listing.set(first, remainder.includes("/") ? "dir" : "file");
      }
    }
    return Array.from(listing.entries())
      .map(([name, type]) => `${type} ${name}`)
      .join("\n");
  } catch (e: unknown) {
    return `Error listing directory: ${
      e instanceof Error ? e.message : String(e)
    }`;
  }
}

async function readFileSnippet(args: Record<string, unknown>): Promise<string> {
  const projectId = String(args._projectId || "");
  const target = String(args.target_file || args.path || args.file || "");
  if (!projectId || !target) return "Error: project id or target_file missing.";
  try {
    const file = await db.projectFile.findFirst({
      where: { projectId, path: target },
      include: { chunks: { take: 1, orderBy: { startLine: "asc" } } },
    });
    if (!file) return "File not found.";
    if (file.content) return file.content.slice(0, 1500);
    if (file.chunks.length > 0) return file.chunks[0].content.slice(0, 1500);
    return "No content stored for this file.";
  } catch (e: unknown) {
    return `Error reading file: ${e instanceof Error ? e.message : String(e)}`;
  }
}
