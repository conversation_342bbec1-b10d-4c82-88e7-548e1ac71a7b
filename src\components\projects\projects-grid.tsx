'use client'

import { ProjectCard } from './project-card'
import { Search } from 'lucide-react'

interface Project {
  id: string
  name: string
  description?: string
  path: string
  language?: string
  framework?: string
  isIndexed: boolean
  indexingStatus: string
  totalFiles: number
  indexedFiles: number
  totalLines: number
  createdAt: string
  lastIndexedAt?: string
}

interface ProjectsGridProps {
  projects: Project[]
  onProjectClick: (projectId: string) => void
  onRefresh?: () => void
}

export function ProjectsGrid({ projects, onProjectClick, onRefresh }: ProjectsGridProps) {
  if (projects.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="flex items-center justify-center mb-4">
          <div className="p-4 rounded-full bg-slate-100 dark:bg-slate-800">
            <Search className="w-8 h-8 text-slate-400" />
          </div>
        </div>
        <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-2">
          No projects found
        </h3>
        <p className="text-slate-500 dark:text-slate-400 max-w-md mx-auto">
          Try adjusting your search query or create a new project to get started.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {projects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          onClick={() => onProjectClick(project.id)}
          onRefresh={onRefresh}
        />
      ))}
    </div>
  )
}
