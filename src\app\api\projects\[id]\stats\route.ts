import { NextRequest, NextResponse } from "next/server";
import { getProjectStats } from "@/lib/indexing";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const stats = await getProjectStats(id);
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error getting project stats:", error);
    return NextResponse.json(
      { error: "Failed to get project statistics" },
      { status: 500 }
    );
  }
}
