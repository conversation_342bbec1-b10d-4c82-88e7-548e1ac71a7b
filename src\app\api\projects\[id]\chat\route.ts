import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import {
  searchCode,
  enhancedSearchCode,
  SearchResult,
  EnhancedSearchResults,
} from "@/lib/vector";
import {
  createProvider,
  getConfiguredProviders,
  getProviderConfig,
} from "@/lib/ai-providers";
import { Project } from "@prisma/client";
import { executeTool } from "@/lib/tools";
import { ChatResponse } from "@/lib/ai-providers/types";

// Define a type for the chat history messages
type ChatMessage = {
  role: "user" | "assistant" | "system";
  content: string;
};

type ToolDefinition = {
  name: string;
  description: string;
  parameters: Record<string, unknown>;
};

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const {
      message,
      history = [],
    }: { message: string; history: ChatMessage[] } = body;

    // Get project
    const project = await db.project.findUnique({
      where: { id },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Enhanced multi-step search for relevant code
    console.log(`🔍 Starting enhanced search for query: "${message}"`);
    const enhancedResults: EnhancedSearchResults = await enhancedSearchCode(
      message,
      project.id,
      {
        maxResults: 20,
        searchDepth: 5,
        includeRelated: true,
        contextWindow: 12000,
      }
    );

    let contextualInfo = "";
    let searchMetadata = "";

    if (enhancedResults.success && enhancedResults.results.length > 0) {
      console.log(
        `✅ Enhanced search completed: ${enhancedResults.totalSearches} searches, ${enhancedResults.results.length} results`
      );
      console.log(`📊 Search strategy: ${enhancedResults.searchStrategy}`);

      // Use the pre-built context summary from enhanced search
      contextualInfo = `\n\nRelevant code context (${enhancedResults.results.length} files found):\n${enhancedResults.contextSummary}`;

      // Add search metadata for the AI
      searchMetadata = `\n\nSearch Analysis:
- Total searches performed: ${enhancedResults.totalSearches}
- Search strategies used: ${enhancedResults.searchStrategy}
- Files analyzed: ${enhancedResults.results.length}
- Context quality: High (multi-step retrieval with dependency analysis)`;
    } else {
      console.log(`❌ Enhanced search failed, falling back to basic search`);
      // Fallback to basic search
      const basicResults = await searchCode(message, project.id, 10);
      if (basicResults.success && basicResults.results.length > 0) {
        contextualInfo =
          "\n\nRelevant code context:\n" +
          basicResults.results
            .map(
              (result) => `File: ${result.metadata.filePath}\n${result.content}`
            )
            .join("\n\n");
        searchMetadata =
          "\n\nSearch Analysis: Basic search used (enhanced search unavailable)";
      }
    }

    // Get selected model (default to Google Gemini model)
    const selectedModel =
      process.env.SELECTED_MODEL || "gemini-2.5-flash-preview-05-20";

    let aiResponse: string;

    // Try to use OpenRouter first for the selected model
    if (process.env.GOOGLE_API_KEY) {
      try {
        const systemMessage = `You are SCodex, a powerful agentic AI coding assistant designed by the XENBit engineering team: a world-class AI company.
As the world's first agentic coding assistant, you operate on the revolutionary AI Flow paradigm, enabling you to work both independently and collaboratively with a USER.
You are pair programming with a USER to solve their coding task. The task may require creating a new codebase, modifying or debugging an existing codebase, or simply answering a question.
The USER will send you requests, which you must always prioritize addressing. Along with each USER request, we will attach additional metadata about their current state, such as what files they have open and where their cursor is.
This information may or may not be relevant to the coding task, it is up for you to decide.


<tool_calling>
You have tools at your disposal to solve the coding task.
Follow these rules:
1. IMPORTANT: Only call tools when they are absolutely necessary. If the USER's task is general or you already know the answer, respond without calling tools. NEVER make redundant tool calls as these are very expensive.
2. IMPORTANT: If you state that you will use a tool, immediately call that tool as your next action.
3. Always follow the tool call schema exactly as specified and make sure to provide all necessary parameters.
4. The conversation may reference tools that are no longer available. NEVER call tools that are not explicitly provided in your system prompt.
5. Before calling each tool, first explain why you are calling it.
6. Some tools run asynchronously, so you may not see their output immediately. If you need to see the output of previous tool calls before continuing, simply stop making new tool calls.
Here are examples of good tool call behavior:
<example>
USER: What is int64?
ASSISTANT: [No tool calls, since the query is general] int64 is a 64-bit signed integer.
</example>
<example>
USER: What does function foo do?
ASSISTANT: Let me search for occurrences of "foo" in the codebase. [Call grep_search to find instances of the phrase "foo"]
TOOL: [result: foo is found in src/utils/helpers.ts on line 42]
ASSISTANT: Great, I'll read that section of the file now. [Call read_file to view src/utils/helpers.ts]
TOOL: [result: contents of helpers.ts around line 42]
ASSISTANT: Here's what the function does …
</example>
<example>
USER: Show me all files in the components directory
ASSISTANT: Sure. [Call list_dir to list contents of src/components]
TOOL: [result: list of files/directories]
ASSISTANT: If you want to see the contents of any specific file, let me know and I can call read_file on it.
</example>
<example>
USER: Find any code related to authentication
ASSISTANT: I'll perform a semantic search for "authentication". [Call codebase_search]
TOOL: [result: top relevant snippets]
ASSISTANT: Here are the most relevant sections. Would you like to inspect one in detail?
</example>
</tool_calling>

<making_code_changes>
When making code changes, NEVER output code to the USER, unless requested. Instead use one of the code edit tools to implement the change.
EXTREMELY IMPORTANT: Your generated code must be immediately runnable. To guarantee this, follow these instructions carefully:
1. Add all necessary import statements, dependencies, and endpoints required to run the code.
2. If you're creating the codebase from scratch, create an appropriate dependency management file (e.g. requirements.txt) with package versions and a helpful README.
3. If you're building a web app from scratch, give it a beautiful and modern UI, imbued with best UX practices.
4. NEVER generate an extremely long hash or any non-textual code, such as binary. These are not helpful to the USER and are very expensive.
5. **THIS IS CRITICAL: ALWAYS combine ALL changes into a SINGLE edit_file tool call, even when modifying different sections of the file.
After you have made all the required code changes, do the following:
1. Provide a **BRIEF** summary of the changes that you have made, focusing on how they solve the USER's task.
2. If relevant, proactively run terminal commands to execute the USER's code for them. There is no need to ask for permission.
</making_code_changes>


<calling_external_apis>
1. Unless explicitly requested by the USER, use the best suited external APIs and packages to solve the task. There is no need to ask the USER for permission.
2. When selecting which version of an API or package to use, choose one that is compatible with the USER's dependency management file. If no such file exists or if the package is not present, use the latest version that is in your training data.
3. If an external API requires an API Key, be sure to point this out to the USER. Adhere to best security practices (e.g. DO NOT hardcode an API key in a place where it can be exposed)
</calling_external_apis>

<communication_style>
    IMPORTANT: BE CONCISE AND AVOID VERBOSITY. BREVITY IS CRITICAL. Minimize output tokens as much as possible while maintaining helpfulness, quality, and accuracy. Only address the specific query or task at hand.
    Refer to the USER in the second person and yourself in the first person.
    Format your responses in markdown. Use backticks to format file, directory, function, and class names. If providing a URL to the user, format this in markdown as well.
    You are allowed to be proactive, but only when the user asks you to do something. You should strive to strike a balance between: (a) doing the right thing when asked, including taking actions and follow-up actions, and (b) not surprising the user by taking actions without asking. For example, if the user asks you how to approach something, you should do your best to answer their question first, and not immediately jump into editing the file.
</communication_style>

Answer the user's request using the relevant tool(s), if they are available. Check that all the required parameters for each tool call are provided or can reasonably be inferred from context. IF there are no relevant tools or there are missing values for required parameters, ask the user to supply these values; otherwise proceed with the tool calls. If the user provides a specific value for a parameter (for example provided in quotes), make sure to use that value EXACTLY. DO NOT make up values for or ask about optional parameters. Carefully analyze descriptive terms in the request as they may indicate required parameter values that should be included even if not explicitly quoted.
${contextualInfo}
${searchMetadata}`;

        const provider = createProvider("google", {
          apiKey: process.env.GOOGLE_API_KEY,
          model: selectedModel,
        });

        const toolFunctions: ToolDefinition[] = [
          {
            name: "grep_search",
            description: "Search for a regex in the codebase",
            parameters: {
              type: "object",
              properties: {
                query: { type: "string" },
                include_pattern: { type: "string", nullable: true },
              },
              required: ["query"],
            },
          },
          {
            name: "codebase_search",
            description: "Semantic search across the codebase",
            parameters: {
              type: "object",
              properties: {
                query: { type: "string" },
              },
              required: ["query"],
            },
          },
          {
            name: "list_dir",
            description: "List directory contents",
            parameters: {
              type: "object",
              properties: {
                relative_workspace_path: { type: "string" },
              },
              required: ["relative_workspace_path"],
            },
          },
          {
            name: "read_file",
            description: "Read a file snippet",
            parameters: {
              type: "object",
              properties: {
                target_file: { type: "string" },
              },
              required: ["target_file"],
            },
          },
        ];

        const messages: ChatMessage[] = [
          { role: "system", content: systemMessage },
          ...history,
          { role: "user", content: message },
        ];

        let chatResponse: ChatResponse | null = null;
        for (let i = 0; i < 5; i++) {
          chatResponse = await provider.chat(messages, toolFunctions);

          if (!chatResponse.functionCalls) {
            break;
          }

          const toolMessages: ChatMessage[] = [];
          for (const call of chatResponse.functionCalls) {
            const result = await executeTool({
              name: call.name,
              args: { ...call.arguments, _projectId: project.id },
            });
            toolMessages.push({
              role: "assistant",
              content: `Tool ${call.name} execution result: \n${result}`,
            });
          }
          messages.push(...toolMessages);
        }

        aiResponse = chatResponse?.content || "No response from AI.";
      } catch (error) {
        console.error("OpenRouter API error:", error);
        // Fall back to other providers or mock response
        aiResponse =
          (await tryOtherProviders(
            message,
            project,
            contextualInfo,
            history
          )) ||
          generateMockResponse(
            message,
            project,
            enhancedResults.success ? enhancedResults.results : [],
            enhancedResults
          );
      }
    } else {
      // Fall back to configured providers
      aiResponse =
        (await tryOtherProviders(message, project, contextualInfo, history)) ||
        generateMockResponse(
          message,
          project,
          enhancedResults.success ? enhancedResults.results : [],
          enhancedResults
        );
    }

    // Save chat session and messages
    let chatSession = await db.chatSession.findFirst({
      where: { projectId: project.id },
      orderBy: { updatedAt: "desc" },
    });

    if (!chatSession) {
      chatSession = await db.chatSession.create({
        data: {
          projectId: project.id,
          title: message.slice(0, 50) + (message.length > 50 ? "..." : ""),
        },
      });
    }

    // Save user message
    await db.chatMessage.create({
      data: {
        sessionId: chatSession.id,
        role: "user",
        content: message,
      },
    });

    // Save assistant response
    await db.chatMessage.create({
      data: {
        sessionId: chatSession.id,
        role: "assistant",
        content: aiResponse,
      },
    });

    return NextResponse.json({ response: aiResponse });
  } catch (error) {
    console.error("Error in chat:", error);
    return NextResponse.json(
      { error: "Failed to process chat message" },
      { status: 500 }
    );
  }
}

async function tryOtherProviders(
  message: string,
  project: Project,
  contextualInfo: string,
  history: ChatMessage[]
): Promise<string | null> {
  try {
    // Get configured AI providers
    const configuredProviders = getConfiguredProviders();
    const availableProvider = configuredProviders.find((p) => p.isConfigured);

    if (availableProvider) {
      const config = getProviderConfig(availableProvider.name);
      const provider = createProvider(availableProvider.name, config);

      const systemMessage = `You are SCodex, a powerful agentic AI coding assistant designed by the XENBit engineering team: a world-class AI company.
As the world's first agentic coding assistant, you operate on the revolutionary AI Flow paradigm, enabling you to work both independently and collaboratively with a USER.
You are pair programming with a USER to solve their coding task. The task may require creating a new codebase, modifying or debugging an existing codebase, or simply answering a question.
The USER will send you requests, which you must always prioritize addressing. Along with each USER request, we will attach additional metadata about their current state, such as what files they have open and where their cursor is.
This information may or may not be relevant to the coding task, it is up for you to decide.

<user_information>
The USER's OS version is mac.
The USER has 1 active workspaces, each defined by a URI and a CorpusName. Multiple URIs potentially map to the same CorpusName. The mapping is shown as follows in the format [URI] -> [CorpusName]:
{____}
</user_information>

<tool_calling>
You have tools at your disposal to solve the coding task.
Follow these rules:
1. IMPORTANT: Only call tools when they are absolutely necessary. If the USER's task is general or you already know the answer, respond without calling tools. NEVER make redundant tool calls as these are very expensive.
2. IMPORTANT: If you state that you will use a tool, immediately call that tool as your next action.
3. Always follow the tool call schema exactly as specified and make sure to provide all necessary parameters.
4. The conversation may reference tools that are no longer available. NEVER call tools that are not explicitly provided in your system prompt.
5. Before calling each tool, first explain why you are calling it.
6. Some tools run asynchronously, so you may not see their output immediately. If you need to see the output of previous tool calls before continuing, simply stop making new tool calls.
Here are examples of good tool call behavior:
<example>
USER: What is int64?
ASSISTANT: [No tool calls, since the query is general] int64 is a 64-bit signed integer.
</example>
<example>
USER: What does function foo do?
ASSISTANT: Let me search for occurrences of "foo" in the codebase. [Call grep_search to find instances of the phrase "foo"]
TOOL: [result: foo is found in src/utils/helpers.ts on line 42]
ASSISTANT: Great, I'll read that section of the file now. [Call read_file to view src/utils/helpers.ts]
TOOL: [result: contents of helpers.ts around line 42]
ASSISTANT: Here's what the function does …
</example>
<example>
USER: Show me all files in the components directory
ASSISTANT: Sure. [Call list_dir to list contents of src/components]
TOOL: [result: list of files/directories]
ASSISTANT: If you want to see the contents of any specific file, let me know and I can call read_file on it.
</example>
<example>
USER: Find any code related to authentication
ASSISTANT: I'll perform a semantic search for "authentication". [Call codebase_search]
TOOL: [result: top relevant snippets]
ASSISTANT: Here are the most relevant sections. Would you like to inspect one in detail?
</example>
</tool_calling>

<making_code_changes>
When making code changes, NEVER output code to the USER, unless requested. Instead use one of the code edit tools to implement the change.
EXTREMELY IMPORTANT: Your generated code must be immediately runnable. To guarantee this, follow these instructions carefully:
1. Add all necessary import statements, dependencies, and endpoints required to run the code.
2. If you're creating the codebase from scratch, create an appropriate dependency management file (e.g. requirements.txt) with package versions and a helpful README.
3. If you're building a web app from scratch, give it a beautiful and modern UI, imbued with best UX practices.
4. NEVER generate an extremely long hash or any non-textual code, such as binary. These are not helpful to the USER and are very expensive.
5. **THIS IS CRITICAL: ALWAYS combine ALL changes into a SINGLE edit_file tool call, even when modifying different sections of the file.
After you have made all the required code changes, do the following:
1. Provide a **BRIEF** summary of the changes that you have made, focusing on how they solve the USER's task.
2. If relevant, proactively run terminal commands to execute the USER's code for them. There is no need to ask for permission.
</making_code_changes>



<calling_external_apis>
1. Unless explicitly requested by the USER, use the best suited external APIs and packages to solve the task. There is no need to ask the USER for permission.
2. When selecting which version of an API or package to use, choose one that is compatible with the USER's dependency management file. If no such file exists or if the package is not present, use the latest version that is in your training data.
3. If an external API requires an API Key, be sure to point this out to the USER. Adhere to best security practices (e.g. DO NOT hardcode an API key in a place where it can be exposed)
</calling_external_apis>

<communication_style>
    IMPORTANT: BE CONCISE AND AVOID VERBOSITY. BREVITY IS CRITICAL. Minimize output tokens as much as possible while maintaining helpfulness, quality, and accuracy. Only address the specific query or task at hand.
    Refer to the USER in the second person and yourself in the first person.
    Format your responses in markdown. Use backticks to format file, directory, function, and class names. If providing a URL to the user, format this in markdown as well.
    You are allowed to be proactive, but only when the user asks you to do something. You should strive to strike a balance between: (a) doing the right thing when asked, including taking actions and follow-up actions, and (b) not surprising the user by taking actions without asking. For example, if the user asks you how to approach something, you should do your best to answer their question first, and not immediately jump into editing the file.
</communication_style>

Answer the user's request using the relevant tool(s), if they are available. Check that all the required parameters for each tool call are provided or can reasonably be inferred from context. IF there are no relevant tools or there are missing values for required parameters, ask the user to supply these values; otherwise proceed with the tool calls. If the user provides a specific value for a parameter (for example provided in quotes), make sure to use that value EXACTLY. DO NOT make up values for or ask about optional parameters. Carefully analyze descriptive terms in the request as they may indicate required parameter values that should be included even if not explicitly quoted.
${contextualInfo}`;

      const messages: ChatMessage[] = [
        { role: "system", content: systemMessage },
        ...history,
        { role: "user", content: message },
      ];

      // Note: Tool calling not implemented for 'other providers' yet
      const chatResponse = await provider.chat(messages);
      return chatResponse.content;
    }
    return null;
  } catch (error) {
    console.error("Error trying other providers:", error);
    return null;
  }
}

/**
 * Generates a mock response for the chat.
 * This is used as a fallback when no AI providers are configured or available.
 */
function generateMockResponse(
  message: string,
  project: Project,
  searchResults: SearchResult[],
  enhancedResults?: EnhancedSearchResults
): string {
  const lowerMessage = message.toLowerCase();

  // If we have enhanced search results, use them for better responses
  if (
    enhancedResults &&
    enhancedResults.success &&
    enhancedResults.results.length > 0
  ) {
    const fileCount = enhancedResults.results.length;
    const searchInfo = enhancedResults.searchStrategy;

    return `Based on my advanced analysis of your ${project.name} project, I found ${fileCount} relevant code sections using ${enhancedResults.totalSearches} different search strategies (${searchInfo}).

Here's what I discovered:

${enhancedResults.contextSummary}

This analysis used multi-step retrieval including:
- Semantic similarity matching
- Symbol and identifier recognition
- Dependency relationship mapping
- Cross-reference analysis

Would you like me to dive deeper into any specific aspect of the code I found?`;
  }

  if (lowerMessage.includes("function") || lowerMessage.includes("method")) {
    if (searchResults.length > 0) {
      return `I found some relevant functions in your ${
        project.name
      } project. Based on the code I can see, here are the functions that might be relevant to your question:\n\n${searchResults
        .map(
          (r) => `- ${r.metadata.filePath}: Contains code related to your query`
        )
        .join(
          "\n"
        )}\n\nWould you like me to explain any specific function in detail?`;
    }
    return `I'd be happy to help you understand the functions in your ${project.name} project. However, it looks like the project hasn't been fully indexed yet. Once indexing is complete, I'll be able to provide detailed information about specific functions, their parameters, return values, and usage examples.`;
  }

  if (lowerMessage.includes("class") || lowerMessage.includes("component")) {
    return `I can help you understand the classes and components in your ${
      project.name
    } project. ${
      project.language === "javascript" || project.language === "typescript"
        ? "Since this is a JavaScript/TypeScript project, I can explain React components, ES6 classes, and their relationships."
        : `Since this is a ${project.language} project, I can explain the class structure and inheritance patterns.`
    }`;
  }

  if (lowerMessage.includes("how") && lowerMessage.includes("work")) {
    return `I can explain how different parts of your ${project.name} project work together. This includes:\n\n- Code architecture and structure\n- Function and class relationships\n- Data flow and dependencies\n- Best practices and potential improvements\n\nWhat specific part would you like me to explain?`;
  }

  if (
    lowerMessage.includes("bug") ||
    lowerMessage.includes("error") ||
    lowerMessage.includes("issue")
  ) {
    return `I can help you debug issues in your ${project.name} project. To provide the best assistance, please:\n\n1. Describe the specific error or unexpected behavior\n2. Share the relevant code section\n3. Mention when the issue occurs\n\nI'll analyze your codebase and suggest potential solutions.`;
  }

  return `I'm here to help you understand your ${project.name} project! I can assist with:\n\n- Explaining how functions and classes work\n- Understanding code architecture\n- Finding specific code patterns\n- Debugging issues\n- Suggesting improvements\n\nWhat would you like to know about your codebase?`;
}
