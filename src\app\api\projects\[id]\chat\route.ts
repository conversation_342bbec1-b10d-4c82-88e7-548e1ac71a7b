import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { enhancedSearchCode } from "@/lib/vector";
import { createProvider, getProviderConfig } from "@/lib/ai-providers";
import { executeTool } from "@/lib/tools";
import { getSelectedModel } from "@/lib/settings";

type ChatMessage = {
  role: "user" | "assistant" | "system";
  content: string;
};

type ToolDefinition = {
  name: string;
  description: string;
  parameters: Record<string, unknown>;
};

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const {
      message,
      history = [],
      stream = true,
    }: { message: string; history: ChatMessage[]; stream?: boolean } = body;

    // Get project
    const project = await db.project.findUnique({
      where: { id },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Get selected model from settings
    const modelSettings = await getSelectedModel();
    console.log(
      `🤖 Using selected model: ${modelSettings.selectedModel} (${modelSettings.provider})`
    );

    // Validate that we have a proper model configuration
    if (!modelSettings.selectedModel || !modelSettings.provider) {
      return NextResponse.json(
        {
          error:
            "No model selected. Please go to Settings and select an AI model.",
          code: "NO_MODEL_SELECTED",
        },
        { status: 400 }
      );
    }

    // Create AI provider with selected model
    let provider;
    try {
      const config = getProviderConfig(modelSettings.provider);
      provider = createProvider(modelSettings.provider, {
        ...config,
        model: modelSettings.selectedModel,
        temperature: modelSettings.temperature,
        maxTokens: modelSettings.maxTokens,
      });

      if (!provider.isConfigured()) {
        return NextResponse.json(
          {
            error: `${modelSettings.provider} provider is not configured. Please check your API keys in environment variables.`,
            code: "PROVIDER_NOT_CONFIGURED",
            provider: modelSettings.provider,
          },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error("Error creating provider:", error);
      return NextResponse.json(
        {
          error: `Failed to create ${modelSettings.provider} provider: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
          code: "PROVIDER_CREATION_FAILED",
          provider: modelSettings.provider,
        },
        { status: 500 }
      );
    }

    // Enhanced search for relevant code
    console.log(`🔍 Starting enhanced search for query: "${message}"`);
    const enhancedResults = await enhancedSearchCode(message, project.id, {
      maxResults: 20,
      searchDepth: 5,
      includeRelated: true,
      contextWindow: 12000,
    });

    let contextualInfo = "";
    let searchMetadata = "";

    if (enhancedResults.success && enhancedResults.results.length > 0) {
      console.log(
        `✅ Enhanced search completed: ${enhancedResults.totalSearches} searches, ${enhancedResults.results.length} results`
      );
      contextualInfo = `\n\nRelevant code context (${enhancedResults.results.length} files found):\n${enhancedResults.contextSummary}`;
      searchMetadata = `\n\nSearch Analysis:
- Total searches performed: ${enhancedResults.totalSearches}
- Search strategies used: ${enhancedResults.searchStrategy}
- Files analyzed: ${enhancedResults.results.length}
- Context quality: High (multi-step retrieval with dependency analysis)`;
    } else {
      console.log(`❌ Enhanced search failed or no results found`);
    }

    // Prepare system message
    const systemMessage = `You are SCodex, a powerful agentic AI coding assistant designed by the XENBit engineering team.
You are pair programming with a USER to solve their coding task. The task may require creating a new codebase, modifying or debugging an existing codebase, or simply answering a question.

<tool_calling>
You have tools at your disposal to solve the coding task.
Follow these rules:
1. IMPORTANT: Only call tools when they are absolutely necessary. If the USER's task is general or you already know the answer, respond without calling tools.
2. IMPORTANT: If you state that you will use a tool, immediately call that tool as your next action.
3. Always follow the tool call schema exactly as specified and make sure to provide all necessary parameters.
4. Before calling each tool, first explain why you are calling it.
</tool_calling>

<communication_style>
IMPORTANT: BE CONCISE AND AVOID VERBOSITY. BREVITY IS CRITICAL. Minimize output tokens as much as possible while maintaining helpfulness, quality, and accuracy.
Format your responses in markdown. Use backticks to format file, directory, function, and class names.
</communication_style>

Answer the user's request using the relevant tool(s), if they are available.
${contextualInfo}
${searchMetadata}`;

    const toolFunctions: ToolDefinition[] = [
      {
        name: "grep_search",
        description: "Search for a regex in the codebase",
        parameters: {
          type: "object",
          properties: {
            query: { type: "string" },
            include_pattern: { type: "string", nullable: true },
          },
          required: ["query"],
        },
      },
      {
        name: "codebase_search",
        description: "Semantic search across the codebase",
        parameters: {
          type: "object",
          properties: {
            query: { type: "string" },
          },
          required: ["query"],
        },
      },
      {
        name: "list_dir",
        description: "List directory contents",
        parameters: {
          type: "object",
          properties: {
            relative_workspace_path: { type: "string" },
          },
          required: ["relative_workspace_path"],
        },
      },
      {
        name: "read_file",
        description: "Read a file snippet",
        parameters: {
          type: "object",
          properties: {
            target_file: { type: "string" },
          },
          required: ["target_file"],
        },
      },
    ];

    const messages: ChatMessage[] = [
      { role: "system", content: systemMessage },
      ...history,
      { role: "user", content: message },
    ];

    // Handle streaming vs non-streaming
    if (stream && provider.chatStream) {
      try {
        const streamResponse = await provider.chatStream(
          messages,
          toolFunctions
        );

        // Return streaming response
        return new NextResponse(streamResponse.stream, {
          headers: {
            "Content-Type": "text/plain; charset=utf-8",
            "Transfer-Encoding": "chunked",
          },
        });
      } catch (error) {
        console.error("Streaming chat error:", error);
        return NextResponse.json(
          {
            error: `Streaming failed: ${
              error instanceof Error ? error.message : "Unknown error"
            }`,
            code: "STREAMING_FAILED",
          },
          { status: 500 }
        );
      }
    } else {
      // Non-streaming response
      try {
        let chatResponse = await provider.chat(messages, toolFunctions);

        // Handle tool calls (up to 5 iterations)
        for (let i = 0; i < 5 && chatResponse.functionCalls; i++) {
          const toolMessages: ChatMessage[] = [];
          for (const call of chatResponse.functionCalls) {
            const result = await executeTool({
              name: call.name,
              args: { ...call.arguments, _projectId: project.id },
            });
            toolMessages.push({
              role: "assistant",
              content: `Tool ${call.name} execution result: \n${result}`,
            });
          }
          messages.push(...toolMessages);
          chatResponse = await provider.chat(messages, toolFunctions);
        }

        // Save chat session and messages
        let chatSession = await db.chatSession.findFirst({
          where: { projectId: project.id },
          orderBy: { updatedAt: "desc" },
        });

        if (!chatSession) {
          chatSession = await db.chatSession.create({
            data: {
              projectId: project.id,
              title: message.slice(0, 50) + (message.length > 50 ? "..." : ""),
            },
          });
        }

        // Save user message
        await db.chatMessage.create({
          data: {
            sessionId: chatSession.id,
            role: "user",
            content: message,
          },
        });

        // Save assistant response
        await db.chatMessage.create({
          data: {
            sessionId: chatSession.id,
            role: "assistant",
            content: chatResponse.content,
          },
        });

        return NextResponse.json({ response: chatResponse.content });
      } catch (error) {
        console.error("Chat error:", error);
        return NextResponse.json(
          {
            error: `Chat failed: ${
              error instanceof Error ? error.message : "Unknown error"
            }`,
            code: "CHAT_FAILED",
          },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error("Error in chat:", error);
    return NextResponse.json(
      {
        error: "Failed to process chat message",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
