'use client'

import { ProjectChatPage } from '@/components/projects/project-chat-page'
import { MainLayout } from '@/components/layout/main-layout'

interface ProjectPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function ProjectPage({ params }: ProjectPageProps) {
  const { id } = await params

  return (
    <MainLayout>
      <ProjectChatPage projectId={id} />
    </MainLayout>
  )
}
