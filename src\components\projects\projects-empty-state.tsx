'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Plus, Folder } from 'lucide-react'

interface ProjectsEmptyStateProps {
  onCreateProject: () => void
}

export function ProjectsEmptyState({ onCreateProject }: ProjectsEmptyStateProps) {
  return (
    <div className="text-center py-16 px-4">
      <div className="flex justify-center mb-4">
        <div className="rounded-lg bg-slate-100 p-4 dark:bg-slate-800/50">
          <Folder className="w-8 h-8 text-slate-500" />
        </div>
      </div>
      <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-50">No Projects Yet</h2>
      <p className="text-sm text-slate-600 dark:text-slate-400 mt-2 mb-6 max-w-sm mx-auto">
        Create your first project to start indexing your codebase and get AI-powered insights.
      </p>
      <Button onClick={onCreateProject}>
        <Plus className="w-4 h-4 mr-2" />
        Create Project
      </Button>
    </div>
  )
}
