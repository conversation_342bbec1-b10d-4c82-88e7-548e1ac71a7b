'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Plus } from 'lucide-react'

interface ProjectsHeaderProps {
  onCreateProject: () => void
}

export function ProjectsHeader({ onCreateProject }: ProjectsHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-slate-900 dark:text-slate-50">Projects</h1>
        <p className="text-sm text-slate-600 dark:text-slate-400">
          Manage and explore your codebase with AI-powered insights.
        </p>
      </div>
      <Button onClick={onCreateProject}>
        <Plus className="w-4 h-4 mr-2" />
        New Project
      </Button>
    </div>
  )
}
