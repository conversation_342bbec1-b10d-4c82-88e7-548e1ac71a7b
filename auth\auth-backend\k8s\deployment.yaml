apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-app
  template:
    metadata:
      labels:
        app: auth-app
    spec:
      containers:
      - name: auth-app
        image: docker.io/xenauth:latest
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: auth-config
        - secretRef:
            name: auth-secrets
