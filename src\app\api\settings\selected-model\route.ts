import { NextRequest, NextResponse } from "next/server";
import { getSelectedModel } from "@/lib/settings";

export async function GET() {
  try {
    const modelSettings = await getSelectedModel();
    
    return NextResponse.json({
      success: true,
      model: modelSettings,
    });
  } catch (error) {
    console.error("Error getting selected model:", error);
    return NextResponse.json(
      { error: "Failed to get selected model" },
      { status: 500 }
    );
  }
}
