'use client'

import { useSidebar } from '@/hooks/use-sidebar'
import { cn } from '@/lib/utils'
import { ReactNode, useEffect, useState } from 'react'

export function SidebarProvider({ children }: { children: ReactNode }) {
  const { isCollapsed, setCollapsed } = useSidebar()
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkScreenWidth = () => {
      setIsMobile(window.innerWidth < 768)
      setCollapsed(window.innerWidth < 768)
    }

    checkScreenWidth()
    window.addEventListener('resize', checkScreenWidth)

    return () => window.removeEventListener('resize', checkScreenWidth)
  }, [setCollapsed])

  return <div className={cn('flex h-full w-full', isCollapsed && isMobile ? 'overflow-hidden' : '')}>{children}</div>
} 