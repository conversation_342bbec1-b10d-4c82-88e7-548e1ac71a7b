import {
  AIProvider,
  ChatMessage,
  ChatResponse,
  StreamingChatResponse,
  ProviderConfig,
} from "./types";

export class OpenRouterProvider implements AIProvider {
  name = "openrouter";
  displayName = "OpenRouter";

  private config: ProviderConfig;

  constructor(config: ProviderConfig) {
    this.config = {
      model: "anthropic/claude-3-sonnet",
      maxTokens: 4000,
      temperature: 0.7,
      baseUrl: "https://openrouter.ai/api/v1",
      ...config,
    };
  }

  async chat(
    messages: ChatMessage[],
    functions?: {
      name: string;
      description: string;
      parameters: Record<string, unknown>;
    }[]
  ): Promise<ChatResponse> {
    if (!this.isConfigured()) {
      throw new Error("OpenRouter API key not configured");
    }

    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.config.apiKey}`,
        "Content-Type": "application/json",
        "HTTP-Referer": "http://localhost:3000",
        "X-Title": "Code Index",
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages.map((msg) => ({
          role: msg.role,
          content: msg.content,
        })),
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        functions: functions?.map((fn) => ({
          name: fn.name,
          description: fn.description,
          parameters: fn.parameters,
        })),
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.statusText}`);
    }

    const data = await response.json();
    const choice = data.choices[0];

    return {
      content: choice.message.content || "",
      functionCalls: choice.message.function_call
        ? [
            {
              name: choice.message.function_call.name,
              arguments: JSON.parse(choice.message.function_call.arguments),
            },
          ]
        : undefined,
      usage: data.usage
        ? {
            promptTokens: data.usage.prompt_tokens,
            completionTokens: data.usage.completion_tokens,
            totalTokens: data.usage.total_tokens,
          }
        : undefined,
    };
  }

  async chatStream(
    messages: ChatMessage[],
    functions?: {
      name: string;
      description: string;
      parameters: Record<string, unknown>;
    }[]
  ): Promise<StreamingChatResponse> {
    if (!this.isConfigured()) {
      throw new Error("OpenRouter API key not configured");
    }

    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.config.apiKey}`,
        "Content-Type": "application/json",
        "HTTP-Referer": "http://localhost:3000",
        "X-Title": "Code Index",
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages.map((msg) => ({
          role: msg.role,
          content: msg.content,
        })),
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: true,
        functions: functions?.map((fn) => ({
          name: fn.name,
          description: fn.description,
          parameters: fn.parameters,
        })),
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.statusText}`);
    }

    if (!response.body) {
      throw new Error("No response body received from OpenRouter");
    }

    // Create a transform stream to parse SSE data
    const stream = new ReadableStream({
      start(controller) {
        const reader = response.body!.getReader();
        const decoder = new TextDecoder();

        function pump(): Promise<void> {
          return reader.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split("\n");

            for (const line of lines) {
              if (line.startsWith("data: ")) {
                const data = line.slice(6);
                if (data === "[DONE]") {
                  controller.close();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (e) {
                  // Ignore parsing errors for malformed chunks
                }
              }
            }

            return pump();
          });
        }

        return pump();
      },
    });

    return { stream };
  }

  isConfigured(): boolean {
    return !!this.config.apiKey;
  }
}
