'use client'

import { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>eft, Send, Code, Copy, Check, Upload, FileText, BarChart3, MessageSquare, Folder } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import Link from 'next/link'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import rehypeRaw from 'rehype-raw'
import 'highlight.js/styles/github-dark.css'
import { cn } from '@/lib/utils'
import { FileUploadDialog } from './file-upload-dialog'
import { ProjectFilesTab } from './project-files-tab'

interface Project {
  id: string
  name: string
  description?: string
  path: string
  language?: string
  framework?: string
  isIndexed: boolean
  indexingStatus: string
  totalFiles: number
  indexedFiles: number
  totalLines: number
  createdAt: string
  lastIndexedAt?: string
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
}

interface ProjectChatPageProps {
  projectId: string
}

export function ProjectChatPage({ projectId }: ProjectChatPageProps) {
  const [project, setProject] = useState<Project | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('chat')
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    fetchProject()
  }, [projectId])

  const fetchProject = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/projects/${projectId}`)
      if (response.ok) {
        const data = await response.json()
        setProject(data.project)
      } else {
        setProject(null)
      }
    } catch (error) {
      console.error('Error fetching project:', error)
      setProject(null)
    } finally {
      setIsLoading(false)
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || isSending) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString(),
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsSending(true)

    try {
      const response = await fetch(`/api/projects/${projectId}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: inputMessage, history: messages }),
      })

      if (response.ok) {
        const data = await response.json()
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: data.response,
          timestamp: new Date().toISOString(),
        }
        setMessages(prev => [...prev, assistantMessage])
      } else {
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: "Sorry, I encountered an error. Please try again.",
          timestamp: new Date().toISOString(),
        }
        setMessages(prev => [...prev, assistantMessage])
      }
    } catch (error) {
      console.error('Error sending message:', error)
       const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: "Sorry, I couldn't connect to the chat service. Please check your connection and try again.",
          timestamp: new Date().toISOString(),
        }
        setMessages(prev => [...prev, assistantMessage])
    } finally {
      setIsSending(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  // Project Status Badge Component
  const ProjectStatusBadge = ({ project }: { project: Project | null }) => {
    if (!project) return null

    const getStatusInfo = (status: string) => {
      switch (status) {
        case 'completed':
          return {
            color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
            text: 'Indexed',
            progress: 100
          }
        case 'indexing':
          return {
            color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
            text: 'Indexing...',
            progress: project.totalFiles > 0 ? (project.indexedFiles / project.totalFiles) * 100 : 0
          }
        case 'failed':
          return {
            color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
            text: 'Failed',
            progress: 0
          }
        default:
          return {
            color: 'bg-slate-100 text-slate-800 dark:bg-slate-900/20 dark:text-slate-400',
            text: 'Pending',
            progress: 0
          }
      }
    }

    const statusInfo = getStatusInfo(project.indexingStatus)

    return (
      <div className="flex items-center gap-3">
        <Badge className={statusInfo.color}>
          {statusInfo.text}
        </Badge>
        {project.totalFiles > 0 && (
          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
            <span>{project.indexedFiles}/{project.totalFiles} files</span>
            <div className="w-20">
              <Progress value={statusInfo.progress} className="h-2" />
            </div>
          </div>
        )}
      </div>
    )
  }

  // Project Overview Tab Component
  const ProjectOverviewTab = ({ project }: { project: Project | null }) => {
    if (!project) return null

    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Files</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{project.totalFiles}</div>
              <p className="text-xs text-slate-600 dark:text-slate-400">
                {project.indexedFiles} indexed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Lines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{project.totalLines.toLocaleString()}</div>
              <p className="text-xs text-slate-600 dark:text-slate-400">
                Lines of code
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Indexing Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{project.totalFiles > 0 ? Math.round((project.indexedFiles / project.totalFiles) * 100) : 0}%</span>
                </div>
                <Progress
                  value={project.totalFiles > 0 ? (project.indexedFiles / project.totalFiles) * 100 : 0}
                  className="h-2"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Project Information</CardTitle>
            <CardDescription>Details about your project</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-slate-600 dark:text-slate-400">Language</label>
                <p className="text-sm">{project.language || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-slate-600 dark:text-slate-400">Framework</label>
                <p className="text-sm">{project.framework || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-slate-600 dark:text-slate-400">Created</label>
                <p className="text-sm">{new Date(project.createdAt).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-slate-600 dark:text-slate-400">Last Indexed</label>
                <p className="text-sm">
                  {project.lastIndexedAt
                    ? new Date(project.lastIndexedAt).toLocaleDateString()
                    : 'Never'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const copyToClipboard = async (text: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading project...</p>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex-1 flex items-center justify-center text-center">
        <div>
          <Code className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">Project Not Found</h2>
          <p className="text-slate-600 dark:text-slate-400 mb-6">
            The project you&apos;re looking for doesn&apos;t exist.
          </p>
          <Link href="/">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Projects
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Project Header */}
      <div className="border-b bg-white dark:bg-slate-800 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Projects
              </Button>
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-slate-900 dark:text-slate-50">
                {project?.name}
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {project?.description || 'No description provided'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <ProjectStatusBadge project={project} />
            <Button
              onClick={() => setIsUploadDialogOpen(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Files
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="border-b bg-white dark:bg-slate-800 px-6">
          <TabsList className="grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="files" className="flex items-center gap-2">
              <Folder className="w-4 h-4" />
              Files
            </TabsTrigger>
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Overview
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="chat" className="h-full m-0 p-0">
            <div className="flex flex-col h-full max-w-5xl mx-auto w-full">
              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto p-6 space-y-6">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn('flex', message.role === 'user' ? 'justify-end' : 'justify-start')}
                  >
                    <div
                      className={cn(
                        'max-w-[85%] group rounded-lg p-4',
                        message.role === 'user'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700'
                      )}
                    >
                      <div className="prose prose-slate dark:prose-invert max-w-none">
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          rehypePlugins={[rehypeRaw, rehypeHighlight]}
                        >
                          {message.content}
                        </ReactMarkdown>
                      </div>
                      {message.role === 'assistant' && (
                        <div className="text-right mt-2">
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => copyToClipboard(message.content, message.id)}
                            className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            {copiedMessageId === message.id ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                {isSending && (
                  <div className="flex justify-start">
                    <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-slate-500 rounded-full animate-pulse"></div>
                        <div className="w-2 h-2 bg-slate-500 rounded-full animate-pulse delay-75"></div>
                        <div className="w-2 h-2 bg-slate-500 rounded-full animate-pulse delay-150"></div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Input Area */}
              <div className="p-4 border-t bg-slate-100/50 dark:bg-slate-900">
                <div className="relative">
                  <Input
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Ask a question about your code..."
                    className="pr-12 h-12"
                    disabled={isSending}
                  />
                  <Button
                    size="icon"
                    className="absolute top-1/2 right-2.5 -translate-y-1/2"
                    onClick={sendMessage}
                    disabled={isSending || !inputMessage.trim()}
                  >
                    <Send className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="files" className="h-full m-0 p-0">
            <ProjectFilesTab projectId={projectId} />
          </TabsContent>

          <TabsContent value="overview" className="h-full m-0 p-0">
            <ProjectOverviewTab project={project} />
          </TabsContent>
        </div>
      </Tabs>

      {/* Upload Dialog */}
      <FileUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        projectId={projectId}
        onUploadComplete={() => {
          fetchProject()
          setIsUploadDialogOpen(false)
        }}
      />
    </div>
  )
}
