'use client'

import { useState, useEffect } from 'react'
import { Save, Eye, EyeOff, CheckCircle, XCircle, Sparkles, RefreshCw, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface Provider {
  name: string
  displayName: string
  isConfigured: boolean
  description: string
  websiteUrl: string
}

interface Model {
  id: string
  name: string
  description: string
  pricing: {
    prompt: string
    completion: string
  }
  contextLength: number
  isFree: boolean
  provider: string
}

export function SettingsPage() {
  const [providers, setProviders] = useState<Provider[]>([])
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({})
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [models, setModels] = useState<Model[]>([])
  const [selectedModel, setSelectedModel] = useState<string>('gemini-2.5-flash-preview-05-20')
  const [isLoadingModels, setIsLoadingModels] = useState(false)

  useEffect(() => {
    fetchProviders()
    fetchModels()
  }, [])

  const fetchProviders = async () => {
    try {
      const response = await fetch('/api/settings/providers')
      if (response.ok) {
        const data = await response.json()
        setProviders(data.providers || [])
      }
    } catch (error) {
      console.error('Error fetching providers:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchModels = async () => {
    setIsLoadingModels(true)
    try {
      const response = await fetch('/api/settings/models')
      if (response.ok) {
        const data = await response.json()
        setModels(data.models || [])
      }
    } catch (error) {
      console.error('Error fetching models:', error)
    } finally {
      setIsLoadingModels(false)
    }
  }

  const handleApiKeyChange = (provider: string, value: string) => {
    setApiKeys(prev => ({ ...prev, [provider]: value }))
  }

  const toggleShowKey = (provider: string) => {
    setShowKeys(prev => ({ ...prev, [provider]: !prev[provider] }))
  }

  const saveSettings = async () => {
    setIsSaving(true)
    setSaveStatus('idle')

    try {
      // Save API keys
      const providersResponse = await fetch('/api/settings/providers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ apiKeys }),
      })

      // Save model selection
      const selectedModelData = models.find(m => m.id === selectedModel)
      const modelsResponse = await fetch('/api/settings/models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedModel,
          provider: selectedModelData?.provider
        }),
      })

      if (providersResponse.ok && modelsResponse.ok) {
        setSaveStatus('success')
        fetchProviders() // Refresh provider status
        setTimeout(() => setSaveStatus('idle'), 3000)
      } else {
        setSaveStatus('error')
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      setSaveStatus('error')
    } finally {
      setIsSaving(false)
    }
  }

  const getProviderInfo = (name: string) => {
    const info = {
      openai: {
        description: 'GPT-4 and other OpenAI models',
        websiteUrl: 'https://platform.openai.com/api-keys',
      },
      anthropic: {
        description: 'Claude 3 and other Anthropic models',
        websiteUrl: 'https://console.anthropic.com/',
      },
      google: {
        description: 'Gemini and other Google AI models',
        websiteUrl: 'https://makersuite.google.com/app/apikey',
      },
      openrouter: {
        description: 'Access to multiple AI models through one API',
        websiteUrl: 'https://openrouter.ai/keys',
      },
    }
    return info[name as keyof typeof info] || { description: '', websiteUrl: '' }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading settings...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Save Status */}
      {saveStatus !== 'idle' && (
        <div className={`flex items-center gap-3 p-4 rounded-xl border shadow-sm ${
          saveStatus === 'success'
            ? 'bg-emerald-50 dark:bg-emerald-950/20 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800'
            : 'bg-red-50 dark:bg-red-950/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800'
        }`}>
          {saveStatus === 'success' ? (
            <CheckCircle className="w-5 h-5" />
          ) : (
            <XCircle className="w-5 h-5" />
          )}
          <span className="font-medium">
            {saveStatus === 'success'
              ? 'Settings saved successfully!'
              : 'Failed to save settings. Please try again.'
            }
          </span>
        </div>
      )}

      {/* AI Providers */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-lg">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-3 text-xl">
            AI Providers
          </CardTitle>
          <CardDescription className="text-base">
            Configure API keys for different AI providers. You need at least one configured provider to use the chat feature.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {providers.map((provider) => {
            const info = getProviderInfo(provider.name)
            return (
              <div key={provider.name} className="border border-slate-200 dark:border-slate-700 rounded-xl p-6 bg-white dark:bg-slate-800/50 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <h3 className="font-semibold text-lg">{provider.displayName}</h3>
                    <Badge variant={provider.isConfigured ? 'default' : 'secondary'}>
                      {provider.isConfigured ? 'Configured' : 'Not Configured'}
                    </Badge>
                  </div>
                  <Button variant="link" asChild>
                    <a href={info.websiteUrl} target="_blank" rel="noopener noreferrer">
                      Get API Key
                    </a>
                  </Button>
                </div>
                <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">{info.description}</p>
                <div className="relative">
                  <Input
                    type={showKeys[provider.name] ? 'text' : 'password'}
                    placeholder={`Enter your ${provider.displayName} API key`}
                    value={apiKeys[provider.name] || ''}
                    onChange={(e) => handleApiKeyChange(provider.name, e.target.value)}
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute inset-y-0 right-0"
                    onClick={() => toggleShowKey(provider.name)}
                  >
                    {showKeys[provider.name] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Model Selection */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-xl">
            <Sparkles className="w-5 h-5 text-purple-500" />
            Default Chat Model
          </CardTitle>
          <CardDescription className="text-base">
            Choose the default model for chat interactions. Models from unconfigured providers will not be available.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingModels ? (
            <div className="flex items-center gap-2 text-slate-500">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span>Loading models...</span>
            </div>
          ) : (
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-full max-w-md">
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                {models.map((model) => (
                  <SelectItem key={model.id} value={model.id} disabled={!providers.find(p => p.name === model.provider)?.isConfigured && !model.isFree}>
                    <div className="flex items-center justify-between">
                      <span>{model.name}</span>
                      {model.isFree && <Badge variant="secondary">Free</Badge>}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </CardContent>
      </Card>
      <div className="flex justify-end pt-4">
        <Button
          onClick={saveSettings}
          disabled={isSaving}
          size="lg"
        >
          {isSaving ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          Save Changes
        </Button>
      </div>
    </div>
  )
}
