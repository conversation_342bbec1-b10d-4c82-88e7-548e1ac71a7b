'use client'

import { useState, useEffect } from 'react'
import { CreateProjectDialog } from './create-project-dialog'
import { ProjectsEmptyState } from './projects-empty-state'
import { ProjectsHeader } from './projects-header'
import { SearchBar } from './search-bar'
import { ProjectsGrid } from './projects-grid'
import { ProjectsLoadingSkeleton } from './projects-loading-skeleton'

interface Project {
  id: string
  name: string
  description?: string
  path: string
  language?: string
  framework?: string
  isIndexed: boolean
  indexingStatus: string
  totalFiles: number
  indexedFiles: number
  totalLines: number
  createdAt: string
  lastIndexedAt?: string
}

export function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchProjects()

    // Set up polling for projects with active indexing
    const interval = setInterval(() => {
      const hasIndexingProjects = projects.some(p => p.indexingStatus === 'indexing')
      if (hasIndexingProjects) {
        fetchProjects()
      }
    }, 2000) // Poll every 2 seconds

    return () => clearInterval(interval)
  }, [projects])

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (response.ok) {
        const data = await response.json()
        setProjects(data.projects || [])
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.language?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleProjectClick = (projectId: string) => {
    window.location.href = `/projects/${projectId}`
  }

  return (
    <>
      {/* Show empty state if no projects and not loading */}
      {projects.length === 0 && !isLoading ? (
        <ProjectsEmptyState onCreateProject={() => setIsCreateDialogOpen(true)} />
      ) : (
        <div className="space-y-8">
          {/* Header */}
          <ProjectsHeader onCreateProject={() => setIsCreateDialogOpen(true)} />
          
          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <SearchBar 
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="Search projects by name, language, or description..."
            />
          </div>
          
          {/* Projects Grid or Loading */}
          {isLoading ? (
            <ProjectsLoadingSkeleton />
          ) : (
            <ProjectsGrid 
              projects={filteredProjects}
              onProjectClick={handleProjectClick}
            />
          )}
        </div>
      )}

      {/* Create Project Dialog */}
      <CreateProjectDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onProjectCreated={fetchProjects}
      />
    </>
  )
}
