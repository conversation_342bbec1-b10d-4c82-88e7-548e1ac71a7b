'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Code, Settings, Home, PanelLeft, Bot, MessageSquare, LifeBuoy } from 'lucide-react'
import { Button, buttonVariants } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useSidebar } from '@/hooks/use-sidebar'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@radix-ui/react-tooltip'

interface NavLink {
  href: string
  label: string
  icon: React.ElementType
}

const mainNavLinks: NavLink[] = [
  { href: '/', label: 'Projects', icon: Home },
  { href: '/chat', label: 'Chat', icon: MessageSquare },
  { href: '/agents', label: 'Agents', icon: Bot },
]

const secondaryNavLinks: NavLink[] = [
  { href: '/settings', label: 'Settings', icon: Settings },
  { href: '/help', label: 'Help & Support', icon: LifeBuoy },
]

export function Sidebar() {
  const pathname = usePathname()
  const { isCollapsed, toggleSidebar } = useSidebar()

  const renderLink = (link: NavLink, isCollapsed: boolean) => {
    const isActive = pathname === link.href
    const LinkIcon = link.icon

    if (isCollapsed) {
      return (
        <TooltipProvider key={link.href}>
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <Link
                href={link.href}
                className={cn(
                  buttonVariants({ variant: isActive ? 'default' : 'ghost', size: 'icon' }),
                  'h-10 w-10',
                  isActive && 'bg-blue-600 hover:bg-blue-600/90'
                )}
              >
                <LinkIcon className="h-5 w-5" />
                <span className="sr-only">{link.label}</span>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="right" className="flex items-center gap-4">
              {link.label}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    return (
      <Link
        key={link.href}
        href={link.href}
        className={cn(
          buttonVariants({ variant: isActive ? 'default' : 'ghost', size: 'sm' }),
          'justify-start gap-3 px-3',
          isActive && 'bg-blue-600 hover:bg-blue-600/90'
        )}
      >
        <LinkIcon className="h-5 w-5" />
        {link.label}
      </Link>
    )
  }

  return (
    <aside
      className={cn(
        'hidden md:flex flex-col justify-between border-r border-slate-200 dark:border-slate-800 bg-slate-50 dark:bg-slate-950 transition-all duration-300 ease-in-out',
        isCollapsed ? 'w-20' : 'w-60'
      )}
    >
      <div>
        <div
          className={cn('flex h-16 items-center border-b border-slate-200 dark:border-slate-800', isCollapsed ? 'justify-center' : 'px-6')}
        >
          <Link href="/" className="flex items-center gap-2 font-bold text-lg">
            <Code className="h-7 w-7 text-blue-600" />
            {!isCollapsed && <span className="dark:text-white">Code Index</span>}
          </Link>
        </div>
        <nav className="flex flex-col gap-1 p-2">
          {mainNavLinks.map(link => renderLink(link, isCollapsed))}
        </nav>
      </div>

      <div className="p-2 border-t border-slate-200 dark:border-slate-800">
        <nav className="flex flex-col gap-1">
          {secondaryNavLinks.map(link => renderLink(link, isCollapsed))}
        </nav>
        <div className={cn('mt-2 border-t pt-2 border-slate-200 dark:border-slate-800', isCollapsed ? 'flex justify-center' : 'px-1')}>
          <Button variant="ghost" size={isCollapsed ? 'icon' : 'default'} onClick={toggleSidebar} className="w-full justify-center gap-3 h-10">
            <PanelLeft className="h-5 w-5" />
            {!isCollapsed && <span className="text-sm">Collapse</span>}
          </Button>
        </div>
      </div>
    </aside>
  )
} 