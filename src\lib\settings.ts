import { db } from "./db";

export interface ModelSettings {
  selectedModel: string;
  provider: string;
  temperature?: number;
  maxTokens?: number;
}

export interface AppSettings {
  model: ModelSettings;
}

// Default settings
const DEFAULT_SETTINGS: AppSettings = {
  model: {
    selectedModel: "gemini-1.5-flash",
    provider: "google",
    temperature: 0.7,
    maxTokens: 4000,
  },
};

// Get settings from database or return defaults
export async function getSettings(): Promise<AppSettings> {
  try {
    // Check if the setting model exists in the database
    if (!db.setting) {
      console.warn("Setting model not available, using defaults");
      return DEFAULT_SETTINGS;
    }

    // Try to get settings from database
    const settingsRecord = await db.setting.findFirst({
      where: { key: "app_settings" },
    });

    if (settingsRecord && settingsRecord.value) {
      const settings = JSON.parse(settingsRecord.value as string);
      return { ...DEFAULT_SETTINGS, ...settings };
    }

    return DEFAULT_SETTINGS;
  } catch (error) {
    console.error("Error getting settings:", error);
    return DEFAULT_SETTINGS;
  }
}

// Save settings to database
export async function saveSettings(
  settings: Partial<AppSettings>
): Promise<void> {
  try {
    const currentSettings = await getSettings();
    const updatedSettings = { ...currentSettings, ...settings };

    await db.setting.upsert({
      where: { key: "app_settings" },
      update: { value: JSON.stringify(updatedSettings) },
      create: {
        key: "app_settings",
        value: JSON.stringify(updatedSettings),
      },
    });
  } catch (error) {
    console.error("Error saving settings:", error);
    throw new Error("Failed to save settings");
  }
}

// Get selected model info
export async function getSelectedModel(): Promise<ModelSettings> {
  const settings = await getSettings();
  return settings.model;
}

// Save selected model
export async function saveSelectedModel(
  modelSettings: Partial<ModelSettings>
): Promise<void> {
  const settings = await getSettings();
  settings.model = { ...settings.model, ...modelSettings };
  await saveSettings(settings);
}

// Determine provider from model ID
export function getProviderFromModel(modelId: string): string {
  if (modelId.includes("gemini") || modelId.includes("google")) {
    return "google";
  }
  if (modelId.includes("gpt") || modelId.includes("openai")) {
    return "openai";
  }
  if (modelId.includes("claude") || modelId.includes("anthropic")) {
    return "anthropic";
  }
  // Default to openrouter for most models
  return "openrouter";
}

// Get model display name from ID
export function getModelDisplayName(modelId: string): string {
  const modelMap: Record<string, string> = {
    "qwen/qwen-2.5-coder-32b-instruct:free": "Qwen 2.5 Coder 32B (Free)",
    "qwen/qwen-2.5-72b-instruct:free": "Qwen 2.5 72B (Free)",
    "meta-llama/llama-3.2-3b-instruct:free": "Llama 3.2 3B (Free)",
    "gemini-1.5-flash": "Gemini 1.5 Flash",
    "gemini-1.5-pro": "Gemini 1.5 Pro",
    "gemini-pro": "Gemini Pro",
    "gpt-4o-mini": "GPT-4o Mini",
    "gpt-4o": "GPT-4o",
    "claude-3-5-haiku-20241022": "Claude 3.5 Haiku",
    "claude-3-5-sonnet-20241022": "Claude 3.5 Sonnet",
  };

  return modelMap[modelId] || modelId;
}
